import { NextResponse } from 'next/server';
import { supabase } from '@/config/env';

export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // Check if email already exists
    const { data: existingEmailData, error: emailError } = await supabase
      .from('early_access')
      .select('id')
      .eq('email', data.email)
      .limit(1);
    
    if (emailError) {
      console.error('Error checking existing email:', emailError);
      return NextResponse.json(
        { error: 'Failed to process early access request' },
        { status: 500 }
      );
    }
    
    if (existingEmailData && existingEmailData.length > 0) {
      return NextResponse.json(
        { message: 'You have already registered for early access with this email.' },
        { status: 200 }
      );
    }
    
    // Check if phone number already exists (if provided)
    if (data.phoneNumber) {
      const { data: existingPhoneData, error: phoneError } = await supabase
        .from('early_access')
        .select('id')
        .eq('phone_number', data.phoneNumber)
        .limit(1);
      
      if (phoneError) {
        console.error('Error checking existing phone number:', phoneError);
        return NextResponse.json(
          { error: 'Failed to process early access request' },
          { status: 500 }
        );
      }
      
      if (existingPhoneData && existingPhoneData.length > 0) {
        return NextResponse.json(
          { message: 'You have already registered for early access with this phone number.' },
          { status: 200 }
        );
      }
    }
    
    // Insert into early_access table
    const { data: insertedData, error } = await supabase
      .from('early_access')
      .insert([
        {
          full_name: data.fullName,
          email: data.email,
          role: data.role,
          phone_number: data.phoneNumber || null,
          created_at: new Date().toISOString()
        }
      ]);

    if (error) {
      console.error('Error inserting early access request:', error);
      return NextResponse.json(
        { error: 'Failed to submit early access request' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: true, message: 'Early access request submitted successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error processing early access request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 
{"name": "@petalshealthai/auth-service", "version": "1.0.0", "description": "Authentication and Session Management Service for PETALSHEALTHAI", "main": "dist/server.js", "scripts": {"dev": "bun --watch src/server.ts", "build": "bun build src/server.ts --outdir ./dist", "start": "bun run dist/server.js", "test": "bun test", "test:watch": "bun test --watch", "test:coverage": "bun test --coverage", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"**/*.{ts,js,json}\"", "migrate": "bun run src/scripts/migrate.ts", "seed": "bun run src/scripts/seed.ts", "health": "curl -f http://localhost:3001/health || exit 1"}, "keywords": ["authentication", "session-management", "jwt", "security", "healthcare", "microservice"], "author": "PETALSHEALTHAI Team", "license": "PROPRIETARY", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "redis": "^4.6.13", "@supabase/supabase-js": "^2.38.5", "dotenv": "^16.3.1", "winston": "^3.11.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "nodemailer": "^6.9.7", "crypto-js": "^4.2.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.1", "@types/express": "^4.17.21", "@types/express-session": "^1.17.10", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/node": "^24.0.10", "@types/nodemailer": "^6.4.14", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "jest": "^29.7.0", "prettier": "^3.6.2", "supertest": "^6.3.3", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "packageManager": "bun@1.0.0"}
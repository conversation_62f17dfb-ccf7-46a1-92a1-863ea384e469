/**
 * Circuit Breaker Model for Express Gateway
 * 
 * This model defines the circuit breaker policy that helps prevent cascading failures
 * by detecting failures and encapsulating the logic of preventing a failure from
 * constantly recurring.
 */

interface FallbackResponse {
  status: number;
  message: string;
}

interface CircuitBreakerModel {
  name: string;
  type: string;
  properties: {
    threshold: {
      type: string;
      description: string;
      default: number;
    };
    resetTimeout: {
      type: string;
      description: string;
      default: number;
    };
    timeout: {
      type: string;
      description: string;
      default: number;
    };
    maxFailures: {
      type: string;
      description: string;
      default: number;
    };
    name: {
      type: string;
      description: string;
      default: string;
    };
    fallbackResponse: {
      type: string;
      description: string;
      properties: {
        status: {
          type: string;
          default: number;
        };
        message: {
          type: string;
          default: string;
        };
      };
    };
  };
  required: string[];
}

const CircuitBreakerModel: CircuitBreakerModel = {
  name: 'CircuitBreaker',
  type: 'object',
  properties: {
    threshold: {
      type: 'number',
      description: 'The threshold percentage of failed requests that triggers the circuit breaker',
      default: 50
    },
    resetTimeout: {
      type: 'number',
      description: 'The time in milliseconds to wait before trying to close the circuit again',
      default: 10000
    },
    timeout: {
      type: 'number',
      description: 'The time in milliseconds to wait for a response before considering the request as failed',
      default: 5000
    },
    maxFailures: {
      type: 'number',
      description: 'The number of consecutive failures required to open the circuit',
      default: 3
    },
    name: {
      type: 'string',
      description: 'A unique name for this circuit breaker instance',
      default: 'default-circuit-breaker'
    },
    fallbackResponse: {
      type: 'object',
      description: 'The response to return when the circuit is open',
      properties: {
        status: {
          type: 'number',
          default: 503
        },
        message: {
          type: 'string',
          default: 'Service temporarily unavailable'
        }
      }
    }
  },
  required: ['name']
};

export default CircuitBreakerModel; 
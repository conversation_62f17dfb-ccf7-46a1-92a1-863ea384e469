/**
 * PETALSHEALTHAI Notification Service
 * 
 * Sends multi-channel notifications (email, SMS, push, WhatsApp).
 * Port: 3008
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from 'dotenv';
import winston from 'winston';

config();

const app = express();
const PORT = process.env.NOTIFICATION_SERVICE_PORT || 3008;

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [ new winston.transports.Console() ],
});

app.use(helmet());
app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy', service: 'notification-service' });
});

app.post('/api/v1/notify', (req, res) => {
    res.status(501).json({ message: 'Not Implemented' });
});

app.listen(PORT, () => {
  logger.info(`Notification Service running on port ${PORT}`);
});

export default app;
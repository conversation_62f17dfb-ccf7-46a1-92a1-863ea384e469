---
description: Guidelines for testing strategies and practices
globs: **/*
alwaysApply: true
---

# Testing Guidelines

This document outlines the testing strategies and practices for the project.

## Testing Levels

### Unit Testing

Unit tests verify that individual components work as expected in isolation.

- **When to Use**: For testing individual functions, methods, and classes.
- **Tools**: Jest, Vitest, or other framework-specific testing libraries.
- **Coverage Target**: Aim for 80%+ coverage of business logic.

```typescript
// Good unit test example
describe('calculateTotal', () => {
  it('should calculate the sum of item prices', () => {
    const items = [
      { id: 1, name: 'Item 1', price: 10 },
      { id: 2, name: 'Item 2', price: 20 },
    ];
    expect(calculateTotal(items)).toBe(30);
  });

  it('should return 0 for empty array', () => {
    expect(calculateTotal([])).toBe(0);
  });

  it('should handle items with zero price', () => {
    const items = [
      { id: 1, name: 'Free Item', price: 0 },
      { id: 2, name: 'Item 2', price: 20 },
    ];
    expect(calculateTotal(items)).toBe(20);
  });
});
```

### Integration Testing

Integration tests verify that multiple components work together correctly.

- **When to Use**: For testing interactions between components, API endpoints, database operations.
- **Tools**: Supertest, database testing libraries, mock servers.
- **Coverage Target**: Test all critical paths and edge cases.

```typescript
// Integration test example for an API endpoint
describe('User API', () => {
  beforeAll(async () => {
    await setupTestDatabase();
  });

  afterAll(async () => {
    await cleanupTestDatabase();
  });

  it('should create a new user', async () => {
    const response = await request(app)
      .post('/api/users')
      .send({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
      });

    expect(response.status).toBe(201);
    expect(response.body).toHaveProperty('id');
    expect(response.body.name).toBe('Test User');
    expect(response.body.email).toBe('<EMAIL>');
    expect(response.body).not.toHaveProperty('password');
  });
});
```

### End-to-End Testing

E2E tests verify that the entire application works as expected from a user's perspective.

- **When to Use**: For testing critical user flows and scenarios.
- **Tools**: Playwright, Cypress, Selenium.
- **Coverage Target**: Cover all critical user journeys.

```typescript
// E2E test example using Playwright
test('user can log in and access dashboard', async ({ page }) => {
  await page.goto('https://example.com/login');
  
  // Fill in login form
  await page.fill('input[name="email"]', '<EMAIL>');
  await page.fill('input[name="password"]', 'password123');
  await page.click('button[type="submit"]');
  
  // Verify redirect to dashboard
  await page.waitForURL('https://example.com/dashboard');
  
  // Verify dashboard elements are present
  await expect(page.locator('h1')).toContainText('Dashboard');
  await expect(page.locator('.user-info')).toBeVisible();
});
```

## Test-Driven Development (TDD)

When implementing new features or fixing bugs, follow the TDD approach:

1. **Write a failing test** that defines the expected behavior.
2. **Implement the minimal code** needed to make the test pass.
3. **Refactor** the code while ensuring tests continue to pass.

## Testing Best Practices

1. **Test Independence**: Each test should be independent and not rely on the state from other tests.
2. **Descriptive Test Names**: Use clear, descriptive names that explain what is being tested and the expected outcome.
3. **Arrange-Act-Assert**: Structure tests with clear setup, action, and verification phases.
4. **Mock External Dependencies**: Use mocks for external services, APIs, and databases when appropriate.
5. **Test Edge Cases**: Include tests for boundary conditions, error cases, and unexpected inputs.
6. **Keep Tests Fast**: Optimize tests to run quickly to encourage frequent testing.
7. **Continuous Integration**: Run tests automatically on every commit.

## Test Verification Strategies

When verifying that a task is complete, use the appropriate testing strategy based on the task type:

### For Backend Features

1. **Unit Tests**: Verify that individual functions and methods work correctly.
2. **API Tests**: Verify that API endpoints return the expected responses for various inputs.
3. **Database Tests**: Verify that data is correctly stored, retrieved, and updated.
4. **Error Handling Tests**: Verify that errors are handled gracefully and appropriate error responses are returned.

### For Frontend Features

1. **Component Tests**: Verify that UI components render correctly and respond to user interactions.
2. **Integration Tests**: Verify that components work together correctly.
3. **Visual Tests**: Verify that the UI appears as expected across different devices and browsers.
4. **Accessibility Tests**: Verify that the UI is accessible to users with disabilities.

### For Full-Stack Features

1. **End-to-End Tests**: Verify that the entire feature works correctly from a user's perspective.
2. **Performance Tests**: Verify that the feature performs adequately under expected load.
3. **Security Tests**: Verify that the feature is secure against common vulnerabilities.

## Test Documentation

When documenting tests in the task's `testStrategy` field, include:

1. **Test Approach**: The types of tests to be used (unit, integration, E2E).
2. **Test Scenarios**: The specific scenarios to be tested.
3. **Expected Outcomes**: What constitutes a successful test.
4. **Edge Cases**: Any specific edge cases that should be tested.
5. **Test Data**: Any specific test data requirements.

Example:

```
Test Strategy:
1. Unit test the calculateTotal function with various item combinations.
2. Integration test the order creation API endpoint with valid and invalid orders.
3. E2E test the checkout flow from cart to order confirmation.
4. Edge cases to test: empty cart, items with zero quantity, maximum order value.
```
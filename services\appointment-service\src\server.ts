/**
 * PETALSHEALTHAI Appointment Service
 * 
 * Manages appointment scheduling, provider availability, and calendar integrations.
 * Port: 3006
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from 'dotenv';
import winston from 'winston';
import { createClient } from '@supabase/supabase-js';

config();

const app = express();
const PORT = process.env.APPOINTMENT_SERVICE_PORT || 3006;

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [ new winston.transports.Console() ],
});

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

app.use(helmet());
app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy', service: 'appointment-service' });
});

app.use('/api/v1/appointments', (req, res) => {
    res.status(501).json({ message: 'Not Implemented' });
});

app.listen(PORT, () => {
  logger.info(`Appointment Service running on port ${PORT}`);
});

export default app;
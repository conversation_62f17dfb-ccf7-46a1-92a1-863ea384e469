# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
bun.lockb

# rules directories
.cursor/
.gemini/
.trae/

# Dependency directories
node_modules/
.venv/
.bun-cache/

# Build and cache
build/
dist/
coverage/
.next/
out/
*.tsbuildinfo
*.bak
*.tmp
README-task-master.md

# Python
__pycache__/
*.py[cod]
*.so
*.egg-info/
CACHEDIR.TAG

# Environment variables
.env
.env.*
*.env.docker

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

# Task files
tasks.json
tasks/
scripts/
documents/
templates/

# Docker
*.env.docker
docker-compose.override.yml

# Misc
*.lock
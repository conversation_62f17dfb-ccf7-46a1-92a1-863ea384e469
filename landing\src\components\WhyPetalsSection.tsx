"use client";

const WhyPetalsSection = () => {
  const features = [
    {
      icon: (
        <svg className="w-12 h-12 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
      ),
      title: 'Personalized Health Companion',
      description: 'Built on the latest research, our Ai understand you- your symptoms, your medicine, your routines',
    },
    {
      icon: (
        <svg className="w-12 h-12 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path>
          <path d="M15 9l-3-3-3 3"></path>
          <path d="M12 6v8"></path>
        </svg>
      ),
      title: 'Smart Supplement & Medication Optimizer',
      description: 'No more guessing. Discover ideal combinations tailored to you',
    },
    {
      icon: (
        <svg className="w-12 h-12 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
        </svg>
      ),
      title: 'Symptom Contextualizer',
      description: 'Cut through the noise. Get explanations and insight that make sense - without internet doomscrolling',
    },
    {
      icon: (
        <svg className="w-12 h-12 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
        </svg>
      ),
      title: 'Built With Experts',
      description: 'Created by health professionals, powered by next gen AI, and co-designed with real patients',
    },
  ];

  return (
    <section id="features" className="py-12 sm:py-16 md:py-20 lg:py-24 bg-[#F2FFF6]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-28">
        <div className="text-center mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900">Why Petals Health AI</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-white rounded-3xl shadow-sm p-8">
              <div className="mb-6">
                {feature.icon}
              </div>
              <h3 className="text-xl md:text-2xl font-semibold mb-3 text-gray-800">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyPetalsSection; 
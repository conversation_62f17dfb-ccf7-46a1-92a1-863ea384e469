# Environment Variables Setup Guide for PETALSHEALTHAI Backend

This guide will help you set up all the necessary environment variables for the PETALSHEALTHAI Backend system.

## 🚀 Quick Start

1. **Copy the template to your .env file:**
   ```bash
   cp scripts/env-template.txt .env
   ```

2. **Follow the sections below to configure each service**

## 📋 Priority Order for Initial Setup

Based on the recommended task sequence, configure these environment variables in order:

### 1. 🏗️ **IMMEDIATE SETUP (Task 11: Project Setup)**

```env
NODE_ENV=development
PYTHON_ENV=development
LOG_LEVEL=info
```

### 2. 🗄️ **DATABASE SETUP (Task 1 & 12: Supabase)**

**Required immediately:**
- Create a Supabase project at [supabase.com](https://supabase.com)
- Get your project URL and API keys from the Supabase dashboard

```env
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

### 3. 🔐 **AUTHENTICATION SETUP (Task 14)**

**Generate secure secrets:**
```bash
# Generate JWT secret (256 bits)
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Generate session secret
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

```env
JWT_SECRET=your-generated-jwt-secret
SESSION_SECRET=your-generated-session-secret
REDIS_URL=redis://localhost:6379
```

### 4. 🌐 **API GATEWAY SETUP (Task 2 & 13)**

```env
GATEWAY_PORT=8080
GATEWAY_ADMIN_PORT=9876
CORS_ORIGIN=http://localhost:3000
```

## 🔧 Service-Specific Configuration

### **External APIs (Task 8) - Configure as needed:**

#### MedlinePlus (Health Information)
- Sign up at: [MedlinePlus Connect API](https://medlineplus.gov/connect/)
- Free for non-commercial use

#### Exercise APIs
- **API Ninjas:** [api.api-ninjas.com](https://api.api-ninjas.com)
- **Free tier:** 50,000 requests/month

#### Air Quality
- **AirVisual:** [iqair.com/air-pollution-data-api](https://www.iqair.com/air-pollution-data-api)
- **Free tier:** 1,000 calls/month

#### Nutrition Data
- **FoodData Central:** [fdc.nal.usda.gov](https://fdc.nal.usda.gov/api-guide.html)
- **Free:** No limits for non-commercial use

### **AI Services (Task 7 & 20-23) - Configure when ready:**

#### OpenAI (Primary)
```env
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-4
```

#### Requestly AI Router (Cost optimization)
```env
REQUESTLY_API_KEY=your-requestly-api-key
```

### **Communication Services (Task 24 & 28):**

#### WhatsApp Business API
- Apply for access: [developers.facebook.com/docs/whatsapp](https://developers.facebook.com/docs/whatsapp)

#### Email (SendGrid)
- Sign up: [sendgrid.com](https://sendgrid.com)
- **Free tier:** 100 emails/day

#### SMS (Twilio)
- Sign up: [twilio.com](https://twilio.com)
- **Free trial:** $15 credit

### **Payment Processing (Task 29):**

#### Stripe
- Sign up: [stripe.com](https://stripe.com)
- Start with test keys for development

## 🔒 Security Best Practices

### **Generate Secure Keys:**

```bash
# Encryption keys (32 bytes)
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Session secrets (64 bytes)
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# API keys (generate unique strings)
node -e "console.log(require('crypto').randomUUID())"
```

### **Compliance Settings (HIPAA/GDPR):**

```env
# Data retention (7 years for medical records)
MEDICAL_RECORD_RETENTION_YEARS=7
USER_DATA_RETENTION_DAYS=2555
AUDIT_LOG_RETENTION_DAYS=2555

# Encryption
ENCRYPTION_ALGORITHM=aes-256-gcm
AUDIT_LOG_ENCRYPTION=true
```

## 🧪 Development vs Production

### **Development Settings:**
```env
NODE_ENV=development
USE_MOCK_PAYMENT=true
USE_MOCK_SMS=true
USE_MOCK_EMAIL=true
ENABLE_PAYMENT_PROCESSING=false
```

### **Production Settings:**
```env
NODE_ENV=production
USE_MOCK_PAYMENT=false
USE_MOCK_SMS=false
USE_MOCK_EMAIL=false
ENABLE_PAYMENT_PROCESSING=true
```

## 📦 Service Ports (for Docker/local development)

The template includes predefined ports for all microservices:
- **Express Gateway:** 8080
- **Auth Service:** 3001
- **User Service:** 3002
- **Medical Records:** 3004
- **Chat Service:** 3005
- **AI Orchestrator:** 3010
- **Analytics:** 3011
- etc.

## 🚨 Important Notes

1. **Never commit `.env` files** to version control
2. **Use different secrets** for each environment (dev/staging/prod)
3. **Rotate secrets regularly** in production
4. **Use strong encryption keys** (minimum 256 bits)
5. **Enable audit logging** for all production environments
6. **Set up monitoring** for failed authentication attempts

## 🔄 Next Steps After Environment Setup

1. Run **Task 11 subtasks** to set up project structure
2. Set up **Supabase database** (Task 1)
3. Configure **Express Gateway** (Task 2)
4. Implement **Authentication Service** (Task 14)

## 📞 Getting API Keys

### **Free Tier APIs (Start Here):**
1. **Supabase** (Database) - Free tier: 2 projects
2. **SendGrid** (Email) - Free tier: 100 emails/day
3. **FoodData Central** - Completely free
4. **MedlinePlus** - Free for non-commercial

### **Paid APIs (Add Later):**
1. **OpenAI** - Pay per token usage
2. **Twilio** - SMS costs vary by country
3. **WhatsApp Business** - Requires approval process
4. **Stripe** - Transaction fees apply

This setup will get you started with the core functionality while allowing you to add premium features incrementally! 🎉 
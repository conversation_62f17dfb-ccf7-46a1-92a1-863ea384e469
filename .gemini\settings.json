{"mcpServers": {"task-master-ai": {"command": "npx", "args": ["-y", "task-master-mcp"], "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************", "PERPLEXITY_API_KEY": "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", "MODEL": "claude-3-7-sonnet-20250219", "PERPLEXITY_MODEL": "sonar-pro", "MAX_TOKENS": "64000", "TEMPERATURE": "0.2", "DEFAULT_SUBTASKS": "5", "DEFAULT_PRIORITY": "medium"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "perplexity-ask": {"command": "npx", "args": ["-y", "server-perplexity-ask"], "env": {"PERPLEXITY_API_KEY": "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"}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"]}}}
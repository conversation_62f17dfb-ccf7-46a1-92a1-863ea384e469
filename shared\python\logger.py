import json
import logging
import sys

def setup_logger(name):
    logger = logging.getLogger(name)
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter('%(message)s'))
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    
    def log_info(message, **kwargs):
        logger.info(json.dumps({"level": "info", "message": message, **kwargs}))
        
    def log_error(message, error=None, **kwargs):
        error_dict = {"level": "error", "message": message, **kwargs}
        if error:
            error_dict["error"] = str(error)
            error_dict["traceback"] = getattr(error, "__traceback__", None)
        logger.error(json.dumps(error_dict))
        
    return {"info": log_info, "error": log_error}

[tool.poetry]
name = "my-fastapi-service"
version = "0.1.0"
description = "A FastAPI microservice."
authors = ["Your Name <<EMAIL>>"]
license = "MIT"

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.110.0"
uvicorn = { extras = ["standard"], version = "^0.29.0" }

[tool.poetry.dev-dependencies]
pytest = "^8.0.0"
pytest-cov = "^5.0.0"
black = "^24.0.0"
flake8 = "^7.0.0"
mypy = "^1.0.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88

[tool.mypy]
ignore_missing_imports = true

[tool.pytest.ini_options]
addopts = "--cov=app --cov-report=term-missing" 
# Petals Health AI

This is the monorepo for Petals Health AI, a comprehensive healthcare platform.

## Project Structure

- `admin/`: Admin panel application
- `documents/`: Project documentation and PRDs
- `landing/`: Landing page application
- `scripts/`: Utility scripts
- `services/`: Microservices (Node.js and Python)
- `shared/`: Shared utility libraries
- `supabase/`: Supabase configurations
- `tasks/`: Task Master task files
- `user/`: User application

## Getting Started

More details on setting up the development environment will be provided in `scripts/environment-setup-guide.md`.

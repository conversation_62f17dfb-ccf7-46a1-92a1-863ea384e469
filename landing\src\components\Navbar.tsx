"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { Button } from "@/components/ui/button"; // Adjust path if your ui folder is elsewhere
import { useRouter } from "next/navigation";
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  SheetClose,
} from "@/components/ui/sheet"; // Adjust path
import { Menu, X } from "lucide-react";

interface NavLink {
  href: string;
  label: string;
}

const navLinks: NavLink[] = [
  { href: "#features", label: "Features" },
  { href: "#how-it-works", label: "How it works" },
  { href: "#early-access", label: "Early Access" },
  { href: "#about-us", label: "About us" },
];

const Navbar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const router = useRouter();

  const handleJoinWaitlist = () => {
    router.push('/early_access');
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between m-auto px-4 md:px-6">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="images/logo_alone.svg" // Replace with your actual logo path in /public
            alt="Petals Ai Logo"
            width={40} // Adjust as needed
            height={40} // Adjust as needed
            className="h-8 w-auto md:h-10" // Responsive logo size
          />
          <span className="font-semibold text-lg md:text-xl text-[#2E475D]">
            {" "}
            {/* Approx color from image */}
            Petals Ai
          </span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-6 text-sm font-medium text-[#2E475D]">
          {navLinks.map((link) => (
            <Link
              key={link.label}
              href={link.href}
              className="hover:text-primary transition-colors"
            >
              {link.label}
            </Link>
          ))}
        </nav>

        {/* Desktop CTA Button */}
        <div className="hidden md:flex items-center">
          <Button
            variant="default"
            className="bg-[#6A8E99] hover:bg-[#597984] text-white rounded-lg px-6" // Approx color from image
            onClick={handleJoinWaitlist}
          >
            Join Waitlist
          </Button>
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6 text-[#2E475D]" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className="w-full max-w-xs sm:max-w-sm bg-background p-0"
              hideCloseButton={true}
            >
              <SheetHeader className="p-3.5 border-b">
                <div className="flex items-center justify-end">
                  <SheetTitle className="text-xl font-semibold text-[#2E475D] sr-only">
                    Navigation Menu
                  </SheetTitle>
                  <SheetClose asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-full"
                    >
                      <X className="h-5 w-5 text-[#2E475D]" />
                      <span className="sr-only">Close menu</span>
                    </Button>
                  </SheetClose>
                </div>
              </SheetHeader>
              <div className="p-6 space-y-4">
                <nav className="grid gap-4 text-[#2E475D]">
                  {navLinks.map((link) => (
                    <Link
                      key={link.label}
                      href={link.href}
                      className="text-lg font-medium hover:text-primary transition-colors py-2"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {link.label}
                    </Link>
                  ))}
                </nav>
                <Button
                  variant="default"
                  className="w-full bg-[#6A8E99] hover:bg-[#597984] text-white rounded-lg py-3 text-base mt-6"
                  onClick={() => {
                    setIsMobileMenuOpen(false);
                    router.push('/early_access');
                  }}
                >
                  Join Waitlist
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Navbar;

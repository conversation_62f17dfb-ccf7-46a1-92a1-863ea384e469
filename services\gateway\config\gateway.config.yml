http:
  port: 8080
admin:
  port: 9876
  hostname: localhost
apiEndpoints:
  health-check:
    host: localhost
    paths: '/health'
  users:
    host: localhost
    paths: '/api/v1/users/*'
  auth:
    host: localhost
    paths: '/api/v1/auth/*'
  medical-records:
    host: localhost
    paths: '/api/v1/medical-records/*'
  chat:
    host: localhost
    paths: '/api/v1/chat/*'
  whatsapp:
    host: localhost
    paths: '/api/v1/whatsapp/*'
  appointments:
    host: localhost
    paths: '/api/v1/appointments/*'
  notifications:
    host: localhost
    paths: '/api/v1/notifications/*'
  staff:
    host: localhost
    paths: '/api/v1/staff/*'
  payment:
    host: localhost
    paths: '/api/v1/payments/*'
  telemedicine:
    host: localhost
    paths: '/api/v1/telemedicine/*'
  laboratory:
    host: localhost
    paths: '/api/v1/laboratory/*'
  iot-wearables:
    host: localhost
    paths: '/api/v1/iot-wearables/*'
  ai-orchestrator:
    host: localhost
    paths: '/api/v1/ai/*'
  analytics:
    host: localhost
    paths: '/api/v1/analytics/*'
  supplement-optimization:
    host: localhost
    paths: '/api/v1/supplement-optimization/*'
  predictive-health:
    host: localhost
    paths: '/api/v1/predictive-health/*'
  mental-health:
    host: localhost
    paths: '/api/v1/mental-health/*'
  emergency-response:
    host: localhost
    paths: '/api/v1/emergency-response/*'
  clinical-decision:
    host: localhost
    paths: '/api/v1/clinical-decision/*'
serviceEndpoints:
  health-check:
    url: 'http://localhost:8080'
  users:
    url: 'http://localhost:3001'
  auth:
    url: 'http://localhost:3002'
  medical-records:
    url: 'http://localhost:3003'
  chat:
    url: 'http://localhost:3004'
  whatsapp:
    url: 'http://localhost:3005'
  appointments:
    url: 'http://localhost:3006'
  notifications:
    url: 'http://localhost:3007'
  staff:
    url: 'http://localhost:3008'
  payment:
    url: 'http://localhost:3009'
  telemedicine:
    url: 'http://localhost:3010'
  laboratory:
    url: 'http://localhost:3011'
  iot-wearables:
    url: 'http://localhost:3012'
  ai-orchestrator:
    url: 'http://localhost:8000'
  analytics:
    url: 'http://localhost:8001'
  supplement-optimization:
    url: 'http://localhost:8002'
  predictive-health:
    url: 'http://localhost:8003'
  mental-health:
    url: 'http://localhost:8004'
  emergency-response:
    url: 'http://localhost:8005'
  clinical-decision:
    url: 'http://localhost:8006'
policies:
  - cors
  - log
  - rate-limit
  - jwt
  - expression
  - circuit-breaker
  - timeout
  - retry
  - proxy
pipelines:
  health-check:
    apiEndpoints:
      - health-check
    policies:
      - proxy:
          - action:
              serviceEndpoint: health-check
  default:
    apiEndpoints:
      - users
      - auth
      - medical-records
      - chat
      - whatsapp
      - appointments
      - notifications
      - staff
      - payment
      - telemedicine
      - laboratory
      - iot-wearables
      - ai-orchestrator
      - analytics
      - supplement-optimization
      - predictive-health
      - mental-health
      - emergency-response
      - clinical-decision
    policies:
      - cors:
        - action:
            origin: '${CORS_ORIGIN}'
            methods: 'GET,POST,PUT,DELETE,PATCH'
            allowedHeaders:
              - 'Authorization'
              - 'Content-Type'
            exposedHeaders:
              - 'X-Total-Count'
            credentials: ${CORS_CREDENTIALS}
      - log:
        - action:
            message: '${req.method} ${req.url} ${res.statusCode} ${res.responseTime}ms'
      - rate-limit:
        - action:
            max: 100
            windowMs: 60000
      - jwt:
        - action:
            secretOrPublicKey: '${JWT_SECRET}'
            checkCredentialExistence: false
      - expression:
        - action:
            jscode: |
              req.headers['x-user-id'] = req.user.id;
              req.headers['x-user-role'] = req.user.role;
              req.headers['x-user-email'] = req.user.email;
      - circuit-breaker:
        - action:
            threshold: 50
            resetTimeout: 10000
            timeout: 5000
            maxFailures: 3
      - timeout:
        - action:
            timeout: 10000
      - retry:
        - action:
            retries: 3
            retryDelay: 500
            retryOn:
              - '5xx'
              - 'network-error'
      - proxy:
        - action:
            serviceEndpoint: '${api.endpoint.name}'

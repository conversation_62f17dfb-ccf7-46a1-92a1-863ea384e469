version: '3.8'
services:
  appointment-service:
    build:
      context: ./services/appointment-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3006:3000"
    env_file: .env
    volumes:
      - ./services/appointment-service:/app
      - /app/node_modules

  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3002:3000"
    env_file: .env
    volumes:
      - ./services/auth-service:/app
      - /app/node_modules

  chat-service:
    build:
      context: ./services/chat-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3004:3000"
    env_file: .env
    volumes:
      - ./services/chat-service:/app
      - /app/node_modules

  iot-wearables-service:
    build:
      context: ./services/iot-wearables-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3012:3000"
    env_file: .env
    volumes:
      - ./services/iot-wearables-service:/app
      - /app/node_modules

  laboratory-service:
    build:
      context: ./services/laboratory-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3011:3000"
    env_file: .env
    volumes:
      - ./services/laboratory-service:/app
      - /app/node_modules

  medical-records-service:
    build:
      context: ./services/medical-records-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3003:3000"
    env_file: .env
    volumes:
      - ./services/medical-records-service:/app
      - /app/node_modules

  notification-service:
    build:
      context: ./services/notification-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3007:3000"
    env_file: .env
    volumes:
      - ./services/notification-service:/app
      - /app/node_modules

  payment-service:
    build:
      context: ./services/payment-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3009:3000"
    env_file: .env
    volumes:
      - ./services/payment-service:/app
      - /app/node_modules

  staff-service:
    build:
      context: ./services/staff-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3008:3000"
    env_file: .env
    volumes:
      - ./services/staff-service:/app
      - /app/node_modules

  telemedicine-service:
    build:
      context: ./services/telemedicine-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3010:3000"
    env_file: .env
    volumes:
      - ./services/telemedicine-service:/app
      - /app/node_modules

  ai-orchestrator-service:
    build:
      context: ./services/ai-orchestrator-service
      dockerfile: ../../Dockerfile.python
    ports:
      - "8000:3000"
    env_file: .env
    volumes:
      - ./services/ai-orchestrator-service:/app

  analytics-service:
    build:
      context: ./services/analytics-service
      dockerfile: ../../Dockerfile.python
    ports:
      - "8001:3000"
    env_file: .env
    volumes:
      - ./services/analytics-service:/app

  clinical-decision-service:
    build:
      context: ./services/clinical-decision-service
      dockerfile: ../../Dockerfile.python
    ports:
      - "8006:3000"
    env_file: .env
    volumes:
      - ./services/clinical-decision-service:/app

  emergency-response-service:
    build:
      context: ./services/emergency-response-service
      dockerfile: ../../Dockerfile.python
    ports:
      - "8005:3000"
    env_file: .env
    volumes:
      - ./services/emergency-response-service:/app

  mental-health-service:
    build:
      context: ./services/mental-health-service
      dockerfile: ../../Dockerfile.python
    ports:
      - "8004:3000"
    env_file: .env
    volumes:
      - ./services/mental-health-service:/app

  predictive-health-service:
    build:
      context: ./services/predictive-health-service
      dockerfile: ../../Dockerfile.python
    ports:
      - "8003:3000"
    env_file: .env
    volumes:
      - ./services/predictive-health-service:/app

  supplement-optimization-service:
    build:
      context: ./services/supplement-optimization-service
      dockerfile: ../../Dockerfile.python
    ports:
      - "8002:3000"
    env_file: .env
    volumes:
      - ./services/supplement-optimization-service:/app

  user-service:
    build:
      context: ./services/user-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3001:3000"
    env_file: .env
    volumes:
      - ./services/user-service:/app
      - /app/node_modules

  whatsapp-service:
    build:
      context: ./services/whatsapp-service
      dockerfile: ../../Dockerfile.nodejs
    ports:
      - "3005:3000"
    env_file: .env
    volumes:
      - ./services/whatsapp-service:/app
      - /app/node_modules

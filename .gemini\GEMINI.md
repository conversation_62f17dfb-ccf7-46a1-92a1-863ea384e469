- **Update all scripts, documentation, and automation to follow this rule.**
- **Reference this rule in all future workflow and troubleshooting documentation.**

---
description: Always update the change log after making changes.
globs:
  - "changes_log.md"
alwaysApply: true
---

- **Always update `changes_log.md` after making any changes to the codebase.**
  - This includes creating, modifying, or deleting files.
  - Add a new entry under the current date with a brief description of the changes.

---
description: Always use Supabase MCP tools over the CLI.
globs:
  - "**/*"
alwaysApply: true
---

- **Always use the Supabase MCP tools when available.**
  - The MCP tools are designed for programmatic use and offer better performance and structured data.
  - The Supabase CLI is intended for user interaction and should only be used as a fallback if a specific functionality is not exposed through the MCP tools.

---
description: Ensure all installations and packages are added to the appropriate folders of each service.
globs:
  - "**/*"
alwaysApply: true
---

- **Always ensure that all installations and packages are added to the appropriate folders of each service.**
  - This means that when installing a package or dependency, it should be installed within the specific service's directory (e.g., `services/auth-service/package.json` for Node.js, or `services/ai-orchestrator-service/requirements.txt` for Python) rather than globally or in the project root, unless it's a shared dependency explicitly managed in a shared directory.
  - Verify that the `package.json`, `requirements.txt`, `pyproject.toml`, or similar dependency management files for each service accurately reflect its specific dependencies.
  - Avoid polluting the root `node_modules` or global Python environments with service-specific dependencies.
  - When using tools like `bun install` or `uv pip install`, ensure you are in the correct service directory or specify the target directory.
  - Example:
    ```bash
    # ✅ DO: Install Bun dependencies within the service directory
    cd services/auth-service
    bun install some-package

    # ✅ DO: Install Python dependencies within the service's virtual environment
    cd services/ai-orchestrator-service
    uv pip install some-python-package

    # ❌ DON'T: Install globally if it's a service-specific dependency
    bun install -g some-package

    # ❌ DON'T: Install in the root if it's a service-specific dependency
    # (from project root)
    bun install services/auth-service/some-package
    ```

---
description: Always follow the rules defined in the .gemini/rules directory.
globs: .gemini/rules/**/*.mdc
alwaysApply: true
---

- **Always adhere to the guidelines and best practices outlined in the .gemini/rules directory.**
  - This ensures consistency, quality, and adherence to project standards across all tasks.
  - Refer to these rules for guidance on project structure, development workflow, and specific technology enforcements.

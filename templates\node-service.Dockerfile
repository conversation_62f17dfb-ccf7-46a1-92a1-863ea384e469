# Node.js/Bun Microservice Dockerfile Template
FROM oven/bun:1.1.13-slim as base

WORKDIR /app

# Install dependencies
COPY bun.lock* package.json ./
RUN bun install --frozen-lockfile

# Copy app code
COPY . .

# Expose port (customize as needed)
EXPOSE 3000

# Healthcheck (customize as needed)
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD curl --fail http://localhost:3000/health || exit 1

# Start app (customize as needed)
CMD ["bun", "run", "start"] 
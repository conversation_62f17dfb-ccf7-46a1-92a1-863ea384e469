"use client";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";

const BenefitsSection = () => {
  const whyWeBuilt = [
    "Ai that personalizes your care, not generalized it",
    "Real humans- Licensed doctor and health experts at your fingertips",
    "A mission to empower, not overwhelm"
  ];

  const earlyAccessFeatures = [
    {
      title: "AI Health Companion (Chat v1)",
      description: "Our foundational AI that answers health questions and provides personalized guidance."
    },
    {
      title: "Early Access to Virtual Consultations",
      description: "Be among the first to connect with healthcare professionals through our platform."
    },
    {
      title: "Supplement & Medication Optimizer (Beta)",
      description: "Track medications, check interactions, and receive personalized recommendations."
    },
    {
      title: "Symptom & Health Tracker with AI Insights",
      description: "Log symptoms and receive pattern analysis and potential trigger identification."
    },
    {
      title: "Founding User Status",
      description: "Influence product development and receive lifetime perks for being an early adopter."
    }
  ];

  return (
    <section className="w-full py-12 px-4 md:px-6 bg-[#F2FFF6]">
      <div className="container mx-auto max-w-6xl">
        {/* Why we build section */}
        <div className="mb-16 text-center">
          <h2 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-8">
            Why we build Petals Ai
          </h2>
          
          <Card className="bg-white shadow-sm border-0 max-w-3xl mx-auto">
            <CardContent className="p-8 sm:p-10">
              <h3 className="text-lg sm:text-xl font-semibold text-[#2E475D] mb-4">
                Healthcare today is broken for people with chronic needs. We're changing that with
              </h3>
              
              <ul className="space-y-4 mt-6">
                {whyWeBuilt.map((point, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-blue-500 mr-2 mt-1">•</span>
                    <span className="text-gray-600">{point}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* What you will get section */}
        <div className="mb-16">
          <h2 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-8 text-center">
            What you will Get with Early Access
          </h2>
          
          <Card className="bg-white shadow-sm border-0 max-w-3xl mx-auto">
            <CardContent className="p-6 sm:p-8">
              <div className="space-y-6">
                {earlyAccessFeatures.map((feature, index) => (
                  <div key={index} className="pb-6 border-b border-gray-100 last:border-0 last:pb-0">
                    <h3 className="text-md sm:text-lg font-semibold text-[#2E475D] mb-1">
                      {feature.title}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Be among the first section */}
        <div className="text-center">
          <h2 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-4">
            Be Among the First
          </h2>
          
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Join our MVP waitlist today and be part of a new era in digital health
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/early_access">
              <Button className="bg-[#7ba7b9] hover:bg-[#6a96a8] text-white px-8 py-2 rounded-md">
                Join Early Access
              </Button>
            </Link>
            <Link href="/demo_request">
              <Button variant="outline" className="border-gray-300 text-gray-600 px-8 py-2 rounded-md">
                Just want an Update
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;

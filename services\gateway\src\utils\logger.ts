// =====================
// Logger Utility
//
// - <PERSON><PERSON> for structured logging to console and files
// - Optionally uses a shared logger if available (for cross-service consistency)
// - See README.md for logging patterns and extension instructions
// =====================
import winston from 'winston';
import path from 'path';
// Use dynamic import to avoid TypeScript rootDir error
let sharedLogger: any = {
  info: (message: string, meta?: object) => {},
  error: (message: string, error?: Error, meta?: object) => {},
};
try {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  sharedLogger = require('../../../../shared/node/logger').logger;
} catch (e) {
  // fallback: no-op if shared logger not found
}

const logDir = path.resolve(__dirname, '../../logs');

const winstonLogger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'api-gateway' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
    new winston.transports.File({ filename: path.join(logDir, 'error.log'), level: 'error', maxsize: 10485760, maxFiles: 7 }),
    new winston.transports.File({ filename: path.join(logDir, 'combined.log'), maxsize: 10485760, maxFiles: 7 }),
  ],
});

export const logger = {
  info: (message: string, meta?: object) => {
    sharedLogger.info(message, meta);
    winstonLogger.info(message, meta);
  },
  error: (message: string, error?: Error, meta?: object) => {
    sharedLogger.error(message, error, meta);
    winstonLogger.error(message, { ...meta, error: error?.message, stack: error?.stack });
  }
}; 
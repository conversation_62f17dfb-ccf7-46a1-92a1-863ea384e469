---
description: Enforces the use of TypeScript for all Node.js backend services.
globs: services/**/*.ts, services/**/*.tsx
alwaysApply: true
---

# Node.js Backend Services - TypeScript Enforcement

All Node.js backend services within this project **MUST** be written in TypeScript. This ensures type safety, better code maintainability, and improved developer experience.

## **Required Configuration:**

- **`tsconfig.json`**: Each Node.js service directory must contain a `tsconfig.json` file that extends the root `tsconfig.json`.
  ```json
  // services/your-service/tsconfig.json
  {
    "extends": "../../tsconfig.json",
    "compilerOptions": {
      "outDir": "./dist",
      "rootDir": "./src"
    },
    "include": ["src/**/*.ts"],
    "exclude": ["node_modules", "dist"]
  }
  ```

- **`package.json` Scripts**: The `package.json` for each Node.js service must include the following scripts:
  - `dev`: For development with hot-reloading (e.g., `bun --watch src/index.ts`).
  - `build`: For compiling TypeScript to JavaScript (e.g., `bun build src/index.ts --outdir ./dist`).
  - `start`: For running the compiled JavaScript (e.g., `bun run dist/index.js`).
  - `lint`: For linting TypeScript files (e.g., `eslint . --ext .ts`).
  - `format`: For formatting code with Prettier (e.g., `prettier --write "**/*.{ts,js,json}"`).
  - `test`: For running tests (e.g., `bun test`).

  ```json
  // services/your-service/package.json
  {
    "scripts": {
      "dev": "bun --watch src/index.ts",
      "build": "bun build src/index.ts --outdir ./dist",
      "start": "bun run dist/index.js",
      "lint": "eslint . --ext .ts",
      "format": "prettier --write "**/*.{ts,js,json}"",
      "test": "bun test"
    }
  }
  ```

- **Development Dependencies**: Each Node.js service must have the following development dependencies installed:
  - `typescript`
  - `@types/node`
  - `eslint`
  - `prettier`
  - `eslint-config-prettier`

  ```bash
  bun add -d typescript @types/node eslint prettier eslint-config-prettier
  ```

- **`bunfig.toml`**: Include a `bunfig.toml` file for optimized Bun settings.
  ```toml
  # services/your-service/bunfig.toml
  [install]
  production = false

  [install.lockfile]
  save = true

  [test]
  coverage = true
  ```

## **Migration Guidelines:**

- Rename all `.js` files to `.ts` (or `.tsx` for React components).
- Add type annotations to functions, variables, and parameters.
- Refactor JavaScript code to leverage TypeScript features (interfaces, types, enums).

## **Verification:**

- Run `bun run build` to ensure successful compilation.
- Run `bun run lint` and `bun run format` to ensure code quality.
- Run `bun test` to verify tests pass.

import { Request, Response, NextFunction } from 'express';
import { EventEmitter } from 'events';

interface CircuitBreakerOptions {
  threshold: number;
  resetTimeout: number;
  timeout: number;
  maxFailures: number;
  name: string;
  fallbackResponse?: {
    status: number;
    message: string;
  };
}

enum CircuitState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN'
}

class CircuitBreaker extends EventEmitter {
  private state: CircuitState;
  private failureCount: number;
  private successCount: number;
  private lastFailureTime: number;
  private resetTimeoutId: NodeJS.Timeout | null;
  private options: CircuitBreakerOptions;

  constructor(options: CircuitBreakerOptions) {
    super();
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = 0;
    this.resetTimeoutId = null;
    this.options = {
      threshold: options.threshold || 50,
      resetTimeout: options.resetTimeout || 10000,
      timeout: options.timeout || 5000,
      maxFailures: options.maxFailures || 3,
      name: options.name || 'default-circuit-breaker',
      fallbackResponse: options.fallbackResponse || {
        status: 503,
        message: 'Service temporarily unavailable'
      }
    };

    this.on('success', this.onSuccess.bind(this));
    this.on('failure', this.onFailure.bind(this));
  }

  private onSuccess(): void {
    if (this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= 2) { // Require 2 consecutive successes to close circuit
        this.close();
      }
    }
  }

  private onFailure(): void {
    this.lastFailureTime = Date.now();
    this.failureCount++;

    if (this.state === CircuitState.CLOSED && this.failureCount >= this.options.maxFailures) {
      this.open();
    }
  }

  private open(): void {
    if (this.state !== CircuitState.OPEN) {
      this.state = CircuitState.OPEN;
      this.emit('open', this.options.name);

      this.resetTimeoutId = setTimeout(() => {
        this.halfOpen();
      }, this.options.resetTimeout);
    }
  }

  private halfOpen(): void {
    this.state = CircuitState.HALF_OPEN;
    this.successCount = 0;
    this.emit('half-open', this.options.name);
  }

  private close(): void {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.emit('close', this.options.name);

    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = null;
    }
  }

  public isOpen(): boolean {
    return this.state === CircuitState.OPEN;
  }

  public isHalfOpen(): boolean {
    return this.state === CircuitState.HALF_OPEN;
  }

  public isClosed(): boolean {
    return this.state === CircuitState.CLOSED;
  }

  public getState(): CircuitState {
    return this.state;
  }
}

// Circuit breaker registry to store and retrieve circuit breakers by name
const circuitBreakerRegistry = new Map<string, CircuitBreaker>();

/**
 * Circuit Breaker Plugin for Express Gateway
 */
export default {
  version: '1.0.0',
  policies: ['circuit-breaker'],
  init: (context: any) => {
    return {
      policy: (actionParams: CircuitBreakerOptions) => {
        // Create or retrieve circuit breaker instance
        let circuitBreaker = circuitBreakerRegistry.get(actionParams.name);
        
        if (!circuitBreaker) {
          circuitBreaker = new CircuitBreaker(actionParams);
          circuitBreakerRegistry.set(actionParams.name, circuitBreaker);
          
          // Log circuit breaker events
          circuitBreaker.on('open', (name) => {
            console.warn(`Circuit ${name} opened due to failures`);
          });
          
          circuitBreaker.on('half-open', (name) => {
            console.info(`Circuit ${name} half-opened, testing service availability`);
          });
          
          circuitBreaker.on('close', (name) => {
            console.info(`Circuit ${name} closed, service is operational`);
          });
        }

        return (req: Request, res: Response, next: NextFunction) => {
          // Check if circuit is open
          if (circuitBreaker.isOpen()) {
            const fallback = actionParams.fallbackResponse || {
              status: 503,
              message: 'Service temporarily unavailable'
            };
            return res.status(fallback.status).json({ error: fallback.message });
          }

          // For half-open state, only allow a limited number of requests through
          if (circuitBreaker.isHalfOpen() && Math.random() > 0.1) { // Allow ~10% of traffic through
            const fallback = actionParams.fallbackResponse || {
              status: 503,
              message: 'Service temporarily unavailable, retrying soon'
            };
            return res.status(fallback.status).json({ error: fallback.message });
          }

          // Set timeout for the request
          const requestTimeout = setTimeout(() => {
            circuitBreaker.emit('failure');
            res.status(504).json({ error: 'Gateway Timeout' });
          }, actionParams.timeout || 5000);

          // Capture the original response methods
          const originalSend = res.send;
          const originalJson = res.json;
          const originalEnd = res.end;

          // Override response methods to track success/failure
          res.send = function(body?: any): Response {
            clearTimeout(requestTimeout);
            
            if (res.statusCode >= 500) {
              circuitBreaker.emit('failure');
            } else {
              circuitBreaker.emit('success');
            }
            
            return originalSend.call(this, body);
          };

          res.json = function(body?: any): Response {
            clearTimeout(requestTimeout);
            
            if (res.statusCode >= 500) {
              circuitBreaker.emit('failure');
            } else {
              circuitBreaker.emit('success');
            }
            
            return originalJson.call(this, body);
          };

          // Use type assertion to handle the complex end method signature
          const originalEndMethod = originalEnd as any;
          res.end = function(this: Response, ...args: any[]): Response {
            clearTimeout(requestTimeout);
            
            if (res.statusCode >= 500) {
              circuitBreaker.emit('failure');
            } else {
              circuitBreaker.emit('success');
            }
            
            return originalEndMethod.apply(this, args);
          };

          next();
        };
      }
    };
  }
}; 
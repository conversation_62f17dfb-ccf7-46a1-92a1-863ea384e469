/**
 * PETALSHEALTHAI Payment Service
 * 
 * Handles payment processing and subscription billing via Stripe.
 * Port: 3009
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from 'dotenv';
import winston from 'winston';
import Stripe from 'stripe';

config();

const app = express();
const PORT = process.env.PAYMENT_SERVICE_PORT || 3009;
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [ new winston.transports.Console() ],
});

app.use(helmet());
app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy', service: 'payment-service' });
});

app.use('/api/v1/payments', (req, res) => {
    res.status(501).json({ message: 'Not Implemented' });
});

app.listen(PORT, () => {
  logger.info(`Payment Service running on port ${PORT}`);
});

export default app;
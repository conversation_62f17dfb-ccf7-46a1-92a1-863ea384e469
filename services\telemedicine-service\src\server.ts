/**
 * PETALSHEALTHAI Telemedicine Service
 * 
 * Manages virtual consultations and video integrations.
 * Port: 3010
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from 'dotenv';
import winston from 'winston';

config();

const app = express();
const PORT = process.env.TELEMEDICINE_SERVICE_PORT || 3010;

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [ new winston.transports.Console() ],
});

app.use(helmet());
app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy', service: 'telemedicine-service' });
});

app.use('/api/v1/telemedicine', (req, res) => {
    res.status(501).json({ message: 'Not Implemented' });
});

app.listen(PORT, () => {
  logger.info(`Telemedicine Service running on port ${PORT}`);
});

export default app;
/**
 * Supabase Authentication Model for Express Gateway
 * 
 * This model defines the Supabase authentication policy that integrates
 * with Supabase Auth for user authentication and authorization.
 */

interface SupabaseAuthModel {
  name: string;
  type: string;
  properties: {
    url: {
      type: string;
      description: string;
      default: string;
    };
    anonKey: {
      type: string;
      description: string;
    };
    serviceKey: {
      type: string;
      description: string;
    };
    jwtSecret: {
      type: string;
      description: string;
    };
    userPoolField: {
      type: string;
      description: string;
      default: string;
    };
    userIdField: {
      type: string;
      description: string;
      default: string;
    };
    roleField: {
      type: string;
      description: string;
      default: string;
    };
    sessionDuration: {
      type: string;
      description: string;
      default: number;
    };
    refreshTokenRotation: {
      type: string;
      description: string;
      default: boolean;
    };
    allowedRedirectURLs: {
      type: string;
      description: string;
      items: {
        type: string;
      };
      default: string[];
    };
  };
  required: string[];
}

const SupabaseAuthModel: SupabaseAuthModel = {
  name: 'SupabaseAuth',
  type: 'object',
  properties: {
    url: {
      type: 'string',
      description: 'The Supabase project URL',
      default: 'https://ctmgkqgrozmefuvcvfwa.supabase.co'
    },
    anonKey: {
      type: 'string',
      description: 'The Supabase anonymous key for public operations'
    },
    serviceKey: {
      type: 'string',
      description: 'The Supabase service key for privileged operations'
    },
    jwtSecret: {
      type: 'string',
      description: 'The JWT secret used to verify Supabase tokens'
    },
    userPoolField: {
      type: 'string',
      description: 'The field in the JWT that contains the user pool ID',
      default: 'iss'
    },
    userIdField: {
      type: 'string',
      description: 'The field in the JWT that contains the user ID',
      default: 'sub'
    },
    roleField: {
      type: 'string',
      description: 'The field in the JWT that contains the user role',
      default: 'role'
    },
    sessionDuration: {
      type: 'number',
      description: 'The duration of a session in seconds',
      default: 3600
    },
    refreshTokenRotation: {
      type: 'boolean',
      description: 'Whether to rotate refresh tokens after use',
      default: true
    },
    allowedRedirectURLs: {
      type: 'array',
      description: 'List of allowed redirect URLs after authentication',
      items: {
        type: 'string'
      },
      default: ['http://localhost:3000/auth/callback']
    }
  },
  required: ['url', 'anonKey']
};

export default SupabaseAuthModel; 
-- Extend the built-in auth.users table
create table public.users (
  id uuid primary key references auth.users(id) on delete cascade,
  email text not null unique,
  full_name text,
  avatar_url text,
  role text not null check (role in ('patient', 'doctor', 'admin')),
  created_at timestamp with time zone default timezone('utc', now()),
  updated_at timestamp with time zone default timezone('utc', now())
);

-- Create staff table with reference to users
create table public.staff (
  id uuid primary key references public.users(id) on delete cascade,
  specialization text,
  license_number text unique,
  years_experience integer
);

-- Create RLS policies
alter table public.users enable row level security;
create policy "Users can view their own profile" on public.users
  for select using (auth.uid() = id);

create index idx_users_email on public.users(email);
create index idx_users_role on public.users(role);
import gateway from 'express-gateway';
import express, { Request, Response, NextFunction, ErrorRequestHandler } from 'express';
import os from 'os';
// @ts-expect-error: uuid has no types unless @types/uuid is installed
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import { logger } from './utils/logger';
import { initSentry, sentryRequestHandler, sentryErrorHandler, captureException } from './utils/sentry';
import { incrementRequests, incrementErrors, recordResponseTime, getMetrics } from './utils/metrics';

// Initialize Sentry (opt-in)
initSentry();

// =====================
// Express Gateway Health/Monitoring Server
//
// - Logs all requests and responses with a unique request ID for traceability
// - Exposes /health for system and service status
// - Exposes /metrics for Prometheus scraping
//
// See README.md for more details.
// =====================

// Health/metrics server
const healthApp = express();

// Middleware: assign request ID, log, and record metrics
healthApp.use((function (req: Request, res: Response, next: NextFunction) {
  const requestId = uuidv4();
  (req as any).requestId = requestId;
  const start = process.hrtime();
  incrementRequests();
  logger.info(`Request: ${req.method} ${req.url}`, { requestId });

  res.on('finish', () => {
    const diff = process.hrtime(start);
    const ms = diff[0] * 1000 + diff[1] / 1e6;
    recordResponseTime(ms);
    logger.info(`Response: ${req.method} ${req.url} ${res.statusCode} - ${ms.toFixed(2)}ms`, { requestId });
  });

  next();
}) as express.RequestHandler);

// Sentry request handler (if enabled)
healthApp.use(sentryRequestHandler());

// Health check endpoint
healthApp.get('/health', (req, res) => {
  const healthData = {
    uptime: process.uptime(),
    timestamp: Date.now(),
    memory: process.memoryUsage(),
    cpu: os.cpus(),
    hostname: os.hostname(),
    services: {
      gateway: 'up',
      database: process.env.DATABASE_STATUS || 'unknown',
    },
  };
  res.json(healthData);
});

// Metrics endpoint (Prometheus style)
//
// Example Prometheus scrape config:
//
// scrape_configs:
//   - job_name: 'express-gateway'
//     static_configs:
//       - targets: ['localhost:8085']
healthApp.get('/metrics', (req, res) => {
  res.set('Content-Type', 'text/plain');
  res.send(getMetrics());
});

// Sentry error handler (if enabled)
healthApp.use(sentryErrorHandler());

// Error logging middleware
healthApp.use((function (err: any, req: Request, res: Response, next: NextFunction) {
  incrementErrors();
  logger.error('Unhandled error', err, { requestId: (req as any).requestId });
  captureException(err);
  res.status(500).json({ error: 'Internal server error' });
}) as ErrorRequestHandler);

// Start health/metrics server
healthApp.listen(8085, () => {
  logger.info('Health/metrics server running on port 8085');
});

// Start Express Gateway
logger.info('Starting Express Gateway');

(gateway as any).run({
  baseDir: path.join(__dirname, '..'),
  plugins: [
    {
      name: 'express-gateway-plugin-oauth2',
      version: '1.0.0',
      enabled: true,
      config: {}
    }
  ]
}).catch((err: any) => {
  logger.error('Error starting Express Gateway', err);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

process.on('uncaughtException', (err: any) => {
  logger.error('Uncaught exception', err);
  captureException(err);
  process.exit(1);
});

process.on('unhandledRejection', (reason: any) => {
  logger.error('Unhandled rejection', reason);
  captureException(reason);
});
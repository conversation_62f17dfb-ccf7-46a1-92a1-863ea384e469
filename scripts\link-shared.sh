#!/bin/bash
# scripts/link-shared.sh

# For Node.js services
for service in services/appointment-service services/auth-service services/chat-service services/iot-wearables-service services/laboratory-service services/medical-records-service services/notification-service services/payment-service services/staff-service services/telemedicine-service services/user-service services/whatsapp-service;
do
  echo "Linking shared Node.js utilities to $service"
  mkdir -p "$service/src/shared"
  ln -sf "$(pwd)/shared/node" "$service/src/shared"
done

# For Python services
for service in services/ai-orchestrator-service services/analytics-service services/clinical-decision-service services/emergency-response-service services/mental-health-service services/predictive-health-service services/supplement-optimization-service;
do
  echo "Linking shared Python utilities to $service"
  mkdir -p "$service/src/shared"
  ln -sf "$(pwd)/shared/python" "$service/src/shared"
done

"use client";
import { Heart, Activity, Monitor, Thermometer, Lock, Signal, Calendar, Pill, Award, Shield } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const KeyFeaturesSection = () => {
  const features = [
    {
      icon: <Heart className="h-10 w-10 text-blue-500" />,
      title: "AI Health Companion",
      description: "Your 24 hours personal health assistant to answer questions and provide guidance."
    },
    {
      icon: <Activity className="h-10 w-10 text-blue-500" />,
      title: "Chronic Disease Management",
      description: "Smart tracking and timely alerts for conditions like diabetes and hypertension."
    },
    {
      icon: <Monitor className="h-10 w-10 text-blue-500" />,
      title: "Virtual Care Access",
      description: "Connect with qualified healthcare providers via video or chat within minutes."
    },
    {
      icon: <Thermometer className="h-10 w-10 text-blue-500" />,
      title: "Intelligent Symptom Assessment",
      description: "Advanced AI analysis to understand symptoms and recommend appropriate care."
    },
    {
      icon: <Lock className="h-10 w-10 text-blue-500" />,
      title: "Secure Health Records",
      description: "Keep all your health data encrypted, organized, and accessible only to you."
    },
    {
      icon: <Signal className="h-10 w-10 text-blue-500" />,
      title: "Remote Monitoring",
      description: "Track vital signs and health metrics from home with seamless device integration."
    },
    {
      icon: <Calendar className="h-10 w-10 text-blue-500" />,
      title: "Streamlined Appointments",
      description: "Book, reschedule, or cancel appointments with just a few taps."
    },
    {
      icon: <Pill className="h-10 w-10 text-blue-500" />,
      title: "Medication Management",
      description: "Never miss a dose with smart reminders and refill notifications."
    },
    {
      icon: <Award className="h-10 w-10 text-blue-500" />,
      title: "Clinician Approved",
      description: "Developed with and endorsed by healthcare professionals."
    },
    {
      icon: <Shield className="h-10 w-10 text-blue-500" />,
      title: "Privacy Protected",
      description: "Full HIPAA and GDPR compliance to safeguard your sensitive health information."
    }
  ];

  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-[#F2FFF6]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#2E475D]">Key Features</h2>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300 bg-white rounded-xl overflow-hidden">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="mb-4 flex items-center justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-[#2E475D] mb-2">{feature.title}</h3>
                <p className="text-sm text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default KeyFeaturesSection; 
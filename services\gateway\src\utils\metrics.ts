// Modular metrics utility for Gateway

let requests = 0;
let errors = 0;
const responseTimes: number[] = [];

export function incrementRequests() {
  requests++;
}

export function incrementErrors() {
  errors++;
}

export function recordResponseTime(ms: number) {
  responseTimes.push(ms);
}

export function getMetrics(): string {
  const avgResponseTime =
    responseTimes.length > 0
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      : 0;
  return (
    `# HELP api_gateway_requests_total Total number of requests\n` +
    `# TYPE api_gateway_requests_total counter\n` +
    `api_gateway_requests_total ${requests}\n` +
    `# HELP api_gateway_errors_total Total number of errors\n` +
    `# TYPE api_gateway_errors_total counter\n` +
    `api_gateway_errors_total ${errors}\n` +
    `# HELP api_gateway_avg_response_time_ms Average response time in ms\n` +
    `# TYPE api_gateway_avg_response_time_ms gauge\n` +
    `api_gateway_avg_response_time_ms ${avgResponseTime.toFixed(2)}\n`
  );
} 
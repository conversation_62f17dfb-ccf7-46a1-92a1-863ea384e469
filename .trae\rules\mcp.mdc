---
description: Reference for MCP (Model Control Protocol) architecture and tools
globs: **/*
alwaysApply: true
---

# MCP (Model Control Protocol) Reference

This document provides an overview of the MCP architecture and available tools for integrating with Trae AI.

## MCP Architecture Overview

The Model Control Protocol (MCP) is a standardized interface for AI models to interact with external tools and services. It enables Trae AI to extend its capabilities beyond text generation by providing access to specialized functionality.

### Key Components

1. **MCP Server**: A service that exposes tools to AI models through a standardized API.
2. **MCP Tools**: Functions exposed by the MCP server that can be called by AI models.
3. **MCP Client**: The component within Trae AI that communicates with MCP servers.

## Using MCP Tools

MCP tools are accessed through the `run_mcp` function with the following structure:

```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.serverName",
  tool_name: "toolName",
  args: {
    // Tool-specific arguments
    param1: "value1",
    param2: "value2"
  }
});
```

### Parameters

- `server_name`: The identifier for the MCP server providing the tool.
- `tool_name`: The name of the specific tool to execute.
- `args`: An object containing the tool's input parameters, following the tool's input schema.

## Available MCP Servers

The following MCP servers are available for use with Trae AI:

### Taskmaster

Provides tools for managing task-driven development workflows.

```javascript
server_name: "mcp.config.usrlocalmcp.taskmaster"
```

Refer to [`taskmaster.mdc`](mdc:.trae/rules/taskmaster.mdc) for a comprehensive list of available tools and their usage.

### File System

Provides tools for interacting with the file system.

```javascript
server_name: "mcp.config.usrlocalmcp.filesystem"
```

Common tools include:
- `read_file`: Reads the contents of a file.
- `write_file`: Writes content to a file.
- `list_directory`: Lists the contents of a directory.
- `create_directory`: Creates a new directory.

### Web Browser

Provides tools for web browsing and interaction.

```javascript
server_name: "mcp.config.usrlocalmcp.browser"
```

Common tools include:
- `navigate`: Navigates to a URL.
- `screenshot`: Takes a screenshot of the current page.
- `extract_content`: Extracts content from the current page.
- `click`: Clicks on an element on the page.

### Database

Provides tools for database operations.

```javascript
server_name: "mcp.config.usrlocalmcp.database"
```

Common tools include:
- `query`: Executes a database query.
- `insert`: Inserts data into a database.
- `update`: Updates data in a database.
- `delete`: Deletes data from a database.

## Best Practices

1. **Error Handling**: Always check for errors in the response from MCP tools and handle them appropriately.
2. **Validation**: Validate input parameters before passing them to MCP tools.
3. **Fallbacks**: Implement fallback mechanisms for when MCP tools are unavailable.
4. **Documentation**: Document the use of MCP tools in your code for future reference.
5. **Security**: Be mindful of security implications when using MCP tools, especially those that interact with external systems.

## Extending MCP

Custom MCP servers can be created to expose additional functionality to Trae AI. This involves:

1. Defining the tools and their input/output schemas.
2. Implementing the tool functionality.
3. Exposing the tools through an MCP server.
4. Registering the MCP server with Trae AI.

Refer to the MCP development documentation for detailed instructions on creating custom MCP servers.
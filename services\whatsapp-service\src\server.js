/**
 * PETALSHEALTHAI WhatsApp Service
 * 
 * Integrates with WhatsApp Business API for multi-channel communication.
 * Port: 3005
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from 'dotenv';
import winston from 'winston';

config();

const app = express();
const PORT = process.env.WHATSAPP_SERVICE_PORT || 3005;

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [ new winston.transports.Console() ],
});

app.use(helmet());
app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy', service: 'whatsapp-service' });
});

app.post('/api/v1/whatsapp/webhook', (req, res) => {
  logger.info('Received WhatsApp webhook:', req.body);
  res.status(200).send('OK');
});

app.listen(PORT, () => {
  logger.info(`WhatsApp Service running on port ${PORT}`);
});

export default app;
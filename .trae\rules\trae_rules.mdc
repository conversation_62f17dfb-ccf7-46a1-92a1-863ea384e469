---
description: Guidelines for creating and maintaining Trae rules
globs: **/*
alwaysApply: true
---

# Trae Rules Guidelines

This document outlines the standards for creating and maintaining Trae rules.

## Rule Structure

Each rule file should follow this structure:

```markdown
---
description: Brief description of what the rule covers
globs: Pattern of files this rule applies to (e.g., **/*.ts)
alwaysApply: true/false (whether rule should be shown even when not matching current file)
---

# Rule Title

Introductory paragraph explaining the purpose and importance of this rule.

## Section Heading

Detailed guidance with examples.

## Another Section

More guidance with examples.

```

## File References

When referencing other files within rules, use the following format:

- For rule files: `[rule_name.mdc](mdc:.trae/rules/rule_name.mdc)`
- For code files: `[filename.ts](file:path/to/filename.ts)`

This ensures proper linking and navigation between rules and code.

## Code Examples

Code examples should be formatted with the appropriate language identifier:

````markdown
```typescript
// Good example
function calculateTotal(items: Item[]): number {
  return items.reduce((total, item) => total + item.price, 0);
}
```

```typescript
// Bad example - avoid this pattern
function calculateTotal(items) {
  let total = 0;
  for (let i = 0; i < items.length; i++) {
    total += items[i].price;
  }
  return total;
}
```
````

## Content Guidelines

1. **Be Specific**: Rules should provide clear, actionable guidance.
2. **Include Examples**: Always include both good and bad examples to illustrate the rule.
3. **Explain Why**: Briefly explain the reasoning behind each rule.
4. **Link to Resources**: Reference official documentation or other resources when applicable.
5. **Cross-Reference**: Link to related rules when appropriate.
6. **Use Consistent Terminology**: Maintain consistent terminology across all rules.

## Maintenance Procedures

1. **Regular Review**: Rules should be reviewed quarterly to ensure they remain current.
2. **Update Examples**: Keep examples up-to-date with the latest codebase patterns.
3. **Version Tracking**: Document significant changes to rules.
4. **Deprecation Process**: Mark outdated rules as deprecated before removal.
5. **Feedback Loop**: Incorporate developer feedback into rule improvements.

## Best Practices

1. **Clarity Over Brevity**: Prioritize clear explanations over concise text.
2. **Consistency**: Maintain consistent formatting and structure across all rules.
3. **Practicality**: Rules should reflect practical, real-world usage patterns.
4. **Scope Limitation**: Each rule file should focus on a specific topic or concern.
5. **Progressive Disclosure**: Present the most important information first, with details following.
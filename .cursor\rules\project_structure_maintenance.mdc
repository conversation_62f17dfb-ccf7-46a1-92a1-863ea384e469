---
description:
globs:
alwaysApply: false
---
# Project Structure Documentation Maintenance

This rule ensures that every service, application, and the main project maintains up-to-date `project_structure.md` documentation.

## **Required Project Structure Files**

- **Main Project**: `project_structure.md` in root directory
- **Each Service**: `services/{service-name}/project_structure.md`
- **Each Frontend App**: `{app-name}/project_structure.md` (landing, admin, user, staff)
- **Scripts & Shared**: `scripts/project_structure.md`, `structure/project_structure.md`

## **When to Update Project Structure Files**

### **Always Update When:**
- **Creating new files or directories**
- **Moving or renaming files/folders**
- **Adding new dependencies (package.json changes)**
- **Modifying configuration files**
- **Adding new routes or endpoints**
- **Creating new components or modules**
- **Setting up new database schemas**
- **Adding environment variables**

### **Update Triggers:**
```typescript
// ✅ DO: Update project_structure.md when creating these
- New React components
- New API routes
- New database models
- New services or utilities
- New configuration files
- New scripts or tools

// ✅ DO: Update after these operations
git add . // Before committing
npm install // After adding dependencies
mkdir new-service // After creating directories
```

## **Project Structure File Format**

### **Standard Template for Services:**
```markdown
# 🏗️ {Service Name} - Project Structure

**Last Updated:** {Current Date}
**Service Type:** {Backend Service/Frontend App/Shared Library}
**Technology Stack:** {Tech stack details}
**Port:** {If applicable}

---

## 📁 **Service Structure**

```
{service-name}/
├── src/
│   ├── controllers/     # API route handlers
│   ├── models/         # Data models
│   ├── routes/         # Route definitions
│   ├── middleware/     # Custom middleware
│   ├── utils/          # Utility functions
│   ├── config/         # Configuration files
│   └── index.ts        # Entry point
├── tests/              # Test files
├── docs/               # Service documentation
├── package.json        # Dependencies
├── tsconfig.json       # TypeScript config
├── .env.example        # Environment template
├── Dockerfile          # Container config
├── project_structure.md # 📍 THIS FILE
└── README.md           # Service overview
```

## 🔧 **Key Components**

| Component | Purpose | Status |
|-----------|---------|--------|
| Controllers | Handle HTTP requests | ✅/📋/⏳ |
| Models | Data structures | ✅/📋/⏳ |
| Routes | API endpoints | ✅/📋/⏳ |

## 🗄️ **Database Schema** (if applicable)

## 🛠️ **Dependencies**

## 📋 **Recent Changes**

| Date | Change | Updated By |
|------|--------|------------|
| {Date} | {Description} | {Name} |
```

### **Standard Template for Frontend Apps:**
```markdown
# 🏗️ {App Name} - Project Structure

**Last Updated:** {Current Date}
**App Type:** {Next.js App/React App/etc.}
**Framework:** {Next.js 14+/React/etc.}
**Port:** {Development port}

---

## 📁 **App Structure**

```
{app-name}/
├── src/
│   ├── app/            # App Router (Next.js)
│   ├── components/     # React components
│   ├── lib/            # Utility libraries
│   ├── hooks/          # Custom React hooks
│   ├── types/          # TypeScript types
│   ├── styles/         # CSS/Styling
│   └── config/         # Configuration
├── public/             # Static assets
├── tests/              # Test files
├── package.json        # Dependencies
├── next.config.ts      # Next.js config
├── tailwind.config.ts  # Tailwind config
├── project_structure.md # 📍 THIS FILE
└── README.md           # App overview
```

## 🧩 **Components**

| Component | Purpose | Status |
|-----------|---------|--------|
| {Component} | {Description} | ✅/📋/⏳ |

## 🎨 **Styling & UI**

## 📋 **Recent Changes**

| Date | Change | Updated By |
|------|--------|------------|
| {Date} | {Description} | {Name} |
```

## **Automation Requirements**

### **Before Any Commit:**
```bash
# ✅ DO: Update relevant project_structure.md files
# Check which files were modified
git diff --name-only

# Update corresponding project_structure.md
# - If files in services/auth-service/ → update services/auth-service/project_structure.md
# - If files in landing/ → update landing/project_structure.md
# - If files in root → update root project_structure.md
```

### **After Creating New Services:**
```bash
# ✅ DO: Create project_structure.md immediately
mkdir services/new-service
cd services/new-service
# Create project_structure.md using template above
# Update main project_structure.md to reference new service
```

### **Task Master Integration:**
- **Always update project_structure.md when marking tasks as 'done'**
- **Update before setting task status to 'done'**
- **Include structure updates in task completion notes**

## **File Location Rules**

### **Service Structure Files:**
```
services/
├── auth-service/
│   └── project_structure.md      # Auth service structure
├── user-service/
│   └── project_structure.md      # User service structure
├── chat-service/
│   └── project_structure.md      # Chat service structure
└── {service-name}/
    └── project_structure.md      # Each service has its own
```

### **Frontend App Structure Files:**
```
landing/
├── project_structure.md          # Landing app structure
admin/
├── project_structure.md          # Admin app structure
user/
├── project_structure.md          # User app structure
staff/
├── project_structure.md          # Staff app structure
```

### **Main Project Structure:**
```
petals_health_ai/
├── project_structure.md          # 📍 MAIN - References all sub-projects
```

## **Update Workflow**

### **1. Before Making Changes:**
- Identify which project/service will be affected
- Note the current structure

### **2. During Development:**
- Create files/folders as needed
- Document new components, routes, or features

### **3. After Changes:**
- Update the relevant `project_structure.md` file(s)
- Update the main `project_structure.md` if new services/apps were added
- Include update timestamp and change description

### **4. Before Committing:**
- Verify all affected `project_structure.md` files are updated
- Ensure consistency between actual structure and documentation

## **Maintenance Commands**

### **Quick Structure Check:**
```bash
# ✅ DO: Verify structure files exist
find . -name "project_structure.md" -type f
```

### **Auto-Generate Structure Overview:**
```bash
# ✅ DO: Use tree command to verify current structure
tree -I 'node_modules|.git|dist|build' > temp_structure.txt
# Compare with documented structure
```

## **Quality Checklist**

### **Every project_structure.md Must Have:**
- [ ] Current timestamp in "Last Updated"
- [ ] Accurate file/folder tree
- [ ] Status indicators (✅ Done, 📋 Planned, ⏳ In Progress)
- [ ] Technology stack information
- [ ] Recent changes log
- [ ] Links to related services/apps

### **Main project_structure.md Must Include:**
- [ ] References to all service structure files
- [ ] References to all app structure files
- [ ] Overall architecture overview
- [ ] Service communication patterns
- [ ] Shared dependencies and configurations

## **Examples**

### **✅ Good Structure Update:**
```markdown
# After adding new API endpoint to auth-service
## 📋 Recent Changes
| Date | Change | Updated By |
|------|--------|------------|
| 2024-12-26 | Added POST /auth/refresh endpoint | Assistant |
| 2024-12-26 | Added JWT refresh token middleware | Assistant |
```

### **❌ Poor Structure Update:**
```markdown
# Missing details, no timestamp
## Recent Changes
- Added some auth stuff
```

## **Integration with Other Rules**

- **Follow [dev_workflow.mdc](mdc:.cursor/rules/dev_workflow.mdc) for task-based updates**
- **Use [taskmaster.mdc](mdc:.cursor/rules/taskmaster.mdc) tools to trigger structure updates**
- **Apply [self_improve.mdc](mdc:.cursor/rules/self_improve.mdc) to refine structure documentation**

---

**📍 Remember:** Every file creation, modification, or deletion should trigger a review of the relevant `project_structure.md` file(s). This documentation is crucial for onboarding, debugging, and maintaining the monorepo architecture.

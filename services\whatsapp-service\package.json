{"name": "@petalshealthai/whatsapp-service", "version": "1.0.0", "description": "WhatsApp Business API integration, cross-channel communication", "main": "dist/server.js", "scripts": {"dev": "bun --watch src/server.ts", "build": "bun build src/server.ts --outdir ./dist", "start": "bun run dist/server.js", "test": "bun test", "health": "curl -f http://localhost:3005/health || exit 1", "lint": "eslint . --ext .ts", "format": "prettier --write \"**/*.{ts,js,json}\""}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "@supabase/supabase-js": "^2.38.5"}, "devDependencies": {"@types/node": "^24.0.10", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "prettier": "^3.6.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "private": true}

-- Create buckets for different file types
insert into storage.buckets (id, name, public) values
  ('profile_images', 'Profile Images', false),
  ('medical_documents', 'Medical Documents', false),
  ('general_attachments', 'General Attachments', false);

-- Set up RLS policies for storage
create policy "Users can view their own profile images" on storage.objects
  for select using (bucket_id = 'profile_images' and auth.uid()::text = (storage.foldername(name))[1]);

create policy "Users can upload their own profile images" on storage.objects
  for insert with check (bucket_id = 'profile_images' and auth.uid()::text = (storage.foldername(name))[1]);

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Request, Response, NextFunction } from 'express';

interface SupabaseAuthConfig {
  url: string;
  anonKey: string;
  serviceKey?: string;
  jwtSecret?: string;
  userPoolField?: string;
  userIdField?: string;
  roleField?: string;
  sessionDuration?: number;
  refreshTokenRotation?: boolean;
  allowedRedirectURLs?: string[];
}

interface SupabaseAuthContext {
  supabase: SupabaseClient;
  config: SupabaseAuthConfig;
}

/**
 * Supabase Authentication Plugin for Express Gateway
 */
export default {
  version: '1.0.0',
  policies: ['supabase-auth'],
  init: (context: any) => {
    const authContext: SupabaseAuthContext = {
      supabase: null as unknown as SupabaseClient,
      config: {
        url: process.env.SUPABASE_URL || '',
        anonKey: process.env.SUPABASE_ANON_KEY || '',
        serviceKey: process.env.SUPABASE_SERVICE_KEY,
        jwtSecret: process.env.JWT_SECRET,
        userPoolField: 'iss',
        userIdField: 'sub',
        roleField: 'role',
        sessionDuration: 3600,
        refreshTokenRotation: true,
        allowedRedirectURLs: ['http://localhost:3000/auth/callback']
      }
    };

    return {
      policy: (actionParams: any) => {
        return (req: Request, res: Response, next: NextFunction) => {
          // Initialize Supabase client if not already initialized
          if (!authContext.supabase) {
            authContext.supabase = createClient(
              authContext.config.url,
              authContext.config.anonKey
            );
          }

          // Extract token from authorization header
          const authHeader = req.headers.authorization;
          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({ error: 'Missing or invalid authorization header' });
          }

          const token = authHeader.split(' ')[1];

          // Verify JWT token
          try {
            // Get user from Supabase auth
            authContext.supabase.auth.getUser(token)
              .then(({ data, error }) => {
                if (error || !data.user) {
                  return res.status(401).json({ error: 'Invalid or expired token' });
                }

                // Add user information to request
                req.user = {
                  id: data.user.id,
                  email: data.user.email,
                  role: data.user.role || 'user',
                  // Add additional user properties as needed
                };

                // Add Supabase client to request for downstream services
                req.supabase = authContext.supabase;

                next();
              })
              .catch(err => {
                console.error('Supabase auth error:', err);
                return res.status(500).json({ error: 'Authentication service error' });
              });
          } catch (err) {
            console.error('Token verification error:', err);
            return res.status(401).json({ error: 'Invalid token format' });
          }
        };
      }
    };
  }
};

// Add TypeScript declarations for Express Request
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email?: string;
        role?: string;
        [key: string]: any;
      };
      supabase?: SupabaseClient;
    }
  }
} 
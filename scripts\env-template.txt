# =============================================================================
# PETALSHEALTHAI Backend Environment Configuration Template
# =============================================================================
# Copy this content to a .env file in your project root and fill in your actual values
# Never commit .env files to version control

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=development
PYTHON_ENV=development
LOG_LEVEL=info

# =============================================================================
# SUPABASE CONFIGURATION (Task 1 & 12 - Database Setup)
# =============================================================================
# Supabase project URL and keys
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

# Database connection (Supabase PostgreSQL)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
DATABASE_DIRECT_URL=postgresql://postgres:<EMAIL>:5432/postgres

# =============================================================================
# AUTHENTICATION & SECURITY (Task 3 & 14)
# =============================================================================
# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-256-bits-minimum
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Session Management
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password
SESSION_SECRET=your-session-secret-key
SESSION_MAX_AGE=86400000

# Password Security
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8

# MFA Configuration
MFA_ISSUER=PetalsHealthAI
MFA_WINDOW=1

# =============================================================================
# API GATEWAY CONFIGURATION (Task 2 & 13)
# =============================================================================
# Express Gateway
GATEWAY_PORT=8080
GATEWAY_ADMIN_PORT=9876

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_STRICT_MAX=1000

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,https://yourdomain.com
CORS_CREDENTIALS=true

# =============================================================================
# MICROSERVICES PORTS (Task 11 - Project Setup)
# =============================================================================
# Node.js Services
AUTH_SERVICE_PORT=3001
USER_SERVICE_PORT=3002
ADMIN_SERVICE_PORT=3003
MEDICAL_RECORD_SERVICE_PORT=3004
CHAT_SERVICE_PORT=3005
STAFF_SERVICE_PORT=3006
APPOINTMENT_SERVICE_PORT=3007
NOTIFICATION_SERVICE_PORT=3008
PAYMENT_SERVICE_PORT=3009

# Python Services
AI_ORCHESTRATOR_PORT=3010
ANALYTICS_SERVICE_PORT=3011
WHATSAPP_SERVICE_PORT=3012

# =============================================================================
# EXTERNAL API INTEGRATIONS (Task 8)
# =============================================================================
# MedlinePlus Connect API
MEDLINEPLUS_API_KEY=your-medlineplus-api-key
MEDLINEPLUS_BASE_URL=https://connect.medlineplus.gov

# Exercise APIs
API_NINJAS_KEY=your-api-ninjas-key
GYMFIT_API_KEY=your-gymfit-api-key

# Air Quality API
AIRVISUAL_API_KEY=your-airvisual-api-key

# Nutrition API (FoodData Central)
FOODDATA_API_KEY=your-fooddata-central-key

# Recipe APIs
SPOONACULAR_API_KEY=your-spoonacular-api-key
EDAMAM_API_KEY=your-edamam-api-key
EDAMAM_APP_ID=your-edamam-app-id

# =============================================================================
# AI & LLM CONFIGURATION (Task 7 & 20-23)
# =============================================================================
# Requestly AI Router
REQUESTLY_API_KEY=your-requestly-api-key
REQUESTLY_BASE_URL=https://api.requestly.com

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2048

# Anthropic Claude (backup)
ANTHROPIC_API_KEY=your-anthropic-api-key

# Google Gemini (backup)
GOOGLE_AI_API_KEY=your-google-ai-api-key

# AI Agent Configuration
AI_AGENT_TIMEOUT=30000
AI_MAX_RETRIES=3
AI_TEMPERATURE=0.7

# =============================================================================
# COMMUNICATION SERVICES (Task 24 & 28)
# =============================================================================
# WhatsApp Business API
WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id
WHATSAPP_ACCESS_TOKEN=your-whatsapp-access-token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your-webhook-verify-token
WHATSAPP_BUSINESS_ACCOUNT_ID=your-business-account-id

# Email Service (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=PetalsHealth AI

# SMS Service (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# =============================================================================
# PAYMENT PROCESSING (Task 29)
# =============================================================================
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_PRICE_ID_BASIC=price_your_basic_plan_id
STRIPE_PRICE_ID_PREMIUM=price_your_premium_plan_id

# =============================================================================
# FILE STORAGE & CDN (Task 12)
# =============================================================================
# Supabase Storage
SUPABASE_STORAGE_BUCKET_PROFILES=profile-images
SUPABASE_STORAGE_BUCKET_MEDICAL=medical-documents
SUPABASE_STORAGE_BUCKET_ATTACHMENTS=general-attachments

# File Upload Limits
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# =============================================================================
# MONITORING & ANALYTICS (Task 30 & 32)
# =============================================================================
# Analytics Configuration
ANALYTICS_RETENTION_DAYS=365
ANALYTICS_BATCH_SIZE=100

# Logging Configuration
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_MAX_FILES=30

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# COMPLIANCE & SECURITY (Task 10 & 33)
# =============================================================================
# Audit Logging
AUDIT_LOG_RETENTION_DAYS=2555
AUDIT_LOG_ENCRYPTION=true

# Data Retention (HIPAA/GDPR Compliance)
USER_DATA_RETENTION_DAYS=2555
DELETED_USER_GRACE_PERIOD_DAYS=30
MEDICAL_RECORD_RETENTION_YEARS=7

# Encryption
ENCRYPTION_ALGORITHM=aes-256-gcm
ENCRYPTION_KEY=your-32-byte-encryption-key-here
FIELD_ENCRYPTION_KEY=your-field-encryption-key

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================
# Feature Flags
ENABLE_WHATSAPP_INTEGRATION=true
ENABLE_AI_SUGGESTIONS=true
ENABLE_PAYMENT_PROCESSING=false
ENABLE_ANALYTICS_TRACKING=true

# Testing Configuration
TEST_DATABASE_URL=postgresql://postgres:testpass@localhost:5433/petals_test
TEST_REDIS_URL=redis://localhost:6380

# Mock Services (for development)
USE_MOCK_PAYMENT=true
USE_MOCK_SMS=true
USE_MOCK_EMAIL=true

# =============================================================================
# DOCKER & DEPLOYMENT
# =============================================================================
# Docker Configuration
COMPOSE_PROJECT_NAME=petals-health-ai
DOCKER_REGISTRY=your-registry.com

# Load Balancer
LOAD_BALANCER_PORT=80
SSL_CERT_PATH=/etc/ssl/certs
SSL_KEY_PATH=/etc/ssl/private

# =============================================================================
# BACKUP & DISASTER RECOVERY
# =============================================================================
# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key

# =============================================================================
# TIMEZONE & LOCALIZATION
# =============================================================================
DEFAULT_TIMEZONE=UTC
SUPPORTED_LOCALES=en-US,es-ES,fr-FR
DATE_FORMAT=YYYY-MM-DD HH:mm:ss 
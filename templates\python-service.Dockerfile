# Python FastAPI Microservice Dockerfile Template
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -m appuser
WORKDIR /app

# Install dependencies
COPY requirements.txt ./
RUN pip install --upgrade pip && pip install -r requirements.txt

# Copy app code
COPY . .

# Change to non-root user
USER appuser

# Expose port (default FastAPI)
EXPOSE 8000

# Healthcheck (customize as needed)
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
  CMD curl --fail http://localhost:8000/health || exit 1

# Start FastAPI app (customize as needed)
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"] 
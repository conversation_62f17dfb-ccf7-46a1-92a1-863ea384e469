
-- Insert test users (passwords are 'password')
insert into auth.users (id, email, encrypted_password) values
  ('d0c27300-c619-4dc7-91f7-2a32b38c59b9', '<EMAIL>', '$2a$10$abcdefghijklmnopqrstuvwxyz'),
  ('8a2bce72-a53e-4ce5-a258-c7edf52f83b8', '<EMAIL>', '$2a$10$abcdefghijklmnopqrstuvwxyz'),
  ('6a25b8f9-c10d-4e4c-b7e2-5e52c7d5c8a9', '<EMAIL>', '$2a$10$abcdefghijklmnopqrstuvwxyz');

-- Insert corresponding user profiles
insert into public.users (id, email, full_name, role) values
  ('d0c27300-c619-4dc7-91f7-2a32b38c59b9', '<EMAIL>', 'Test Patient', 'patient'),
  ('8a2bce72-a53e-4ce5-a258-c7edf52f83b8', '<EMAIL>', 'Dr. Test Doctor', 'doctor'),
  ('6a25b8f9-c10d-4e4c-b7e2-5e52c7d5c8a9', '<EMAIL>', 'Admin User', 'admin');

---
description: Guidelines for maintaining project_structure.md documentation
globs: **/*
alwaysApply: true
---

# Project Structure Maintenance Rules

These rules govern when and how to update the `project_structure.md` documentation files across the project.

## Main Project Structure Documentation

The main `project_structure.md` file in the root directory should be updated when:

- Creating new directories or files in the root directory
- Moving or renaming directories or files in the root directory
- Adding new services or frontend applications
- Modifying the overall architecture of the project
- Adding new dependencies to the project
- Changing configuration files in the root directory

## Service Structure Documentation

Each service should have its own `project_structure.md` file in its root directory. This file should be updated when:

- Creating new directories or files in the service
- Moving or renaming directories or files in the service
- Adding new dependencies to the service
- Changing configuration files in the service
- Modifying the database schema
- Adding new API endpoints
- Changing the service's architecture

## Frontend Structure Documentation

Each frontend application should have its own `project_structure.md` file in its root directory. This file should be updated when:

- Creating new directories or files in the frontend application
- Moving or renaming directories or files in the frontend application
- Adding new dependencies to the frontend application
- Changing configuration files in the frontend application
- Adding new components, pages, or routes
- Modifying the state management approach
- Changing the styling approach

## Standard Templates

### Service Template

```markdown
# [Service Name] Structure

## Overview

[Brief description of the service's purpose and functionality]

## Directory Structure

- `/src`: Source code
  - `/controllers`: API endpoint controllers
  - `/models`: Data models
  - `/services`: Business logic
  - `/utils`: Utility functions
  - `/middleware`: Express middleware
  - `/routes`: API route definitions
  - `/config`: Configuration files
  - `/types`: TypeScript type definitions
- `/tests`: Test files
  - `/unit`: Unit tests
  - `/integration`: Integration tests
  - `/e2e`: End-to-end tests
- `/scripts`: Utility scripts
- `/docs`: Documentation

## Key Files

- `package.json`: Node.js dependencies and scripts
- `.env.example`: Example environment variables
- `tsconfig.json`: TypeScript configuration
- `jest.config.js`: Jest test configuration
- `Dockerfile`: Container definition
- `docker-compose.yml`: Local development setup

## Database Schema

[Description of the database schema, tables, and relationships]

## API Endpoints

[List of API endpoints with methods, routes, and brief descriptions]

## Dependencies

[List of key dependencies and their purposes]

## Development Workflow

[Instructions for local development, testing, and deployment]
```

### Frontend Template

```markdown
# [Frontend App Name] Structure

## Overview

[Brief description of the frontend application's purpose and functionality]

## Directory Structure

- `/src`: Source code
  - `/components`: Reusable UI components
    - `/common`: Shared components
    - `/layout`: Layout components
    - `/forms`: Form components
  - `/pages`: Page components
  - `/hooks`: Custom React hooks
  - `/utils`: Utility functions
  - `/services`: API service functions
  - `/store`: State management
  - `/styles`: Global styles
  - `/types`: TypeScript type definitions
  - `/assets`: Static assets
- `/public`: Public assets
- `/tests`: Test files
  - `/unit`: Unit tests
  - `/integration`: Integration tests
  - `/e2e`: End-to-end tests

## Key Files

- `package.json`: Node.js dependencies and scripts
- `.env.example`: Example environment variables
- `tsconfig.json`: TypeScript configuration
- `vite.config.ts`: Vite configuration
- `index.html`: HTML entry point

## Routing Structure

[Description of the routing structure and navigation flow]

## State Management

[Description of the state management approach]

## Styling Approach

[Description of the styling approach and design system]

## Dependencies

[List of key dependencies and their purposes]

## Development Workflow

[Instructions for local development, testing, and deployment]
```
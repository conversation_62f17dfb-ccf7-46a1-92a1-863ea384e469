# Express Gateway Service

## Overview
This gateway routes and secures API traffic for all microservices in the Petals Health AI platform. It provides cross-cutting concerns such as CORS, logging, monitoring, and resilience patterns.

---

## CORS Policy
- **Current:** Open for all GET requests from any origin; other methods use the `${CORS_ORIGIN}` env variable.
- **Config:** See `config/gateway.config.yml` under the `policies` section.
- **How to restrict:** Set `CORS_ORIGIN` to your allowed domain(s) and adjust allowed methods/headers as needed.
- **Note:** See in-file comments for more details.

---

## Logging
- **Library:** Uses Winston for structured logging to console and rotating files (`logs/`).
- **Request IDs:** Each request is assigned a unique ID for traceability.
- **Extending:** To add more fields (e.g., user ID), update the logger calls in `src/server.ts`.
- **Shared logger:** Optionally uses a shared logger from `shared/node/logger` if available.
- **Config:** See `src/utils/logger.ts` and in-file comments.

---

## Monitoring
- **Health endpoint:** `GET /health` returns system and service status.
- **Metrics endpoint:** `GET /metrics` exposes Prometheus-style metrics.
- **Prometheus scrape example:**
  ```yaml
  scrape_configs:
    - job_name: 'express-gateway'
      static_configs:
        - targets: ['localhost:8085']
  ```
- **Config:** See `src/server.ts` and in-file comments.

---

## Resilience Patterns
- **Policies:** Circuit breaker, timeout, and retry policies are defined in `config/gateway.config.yml` and applied to all critical pipelines.
- **How to adjust:** Edit thresholds, timeouts, and retry counts in the config file as needed.
- **Fallbacks:** Add custom error handling in the gateway if you want more than the default proxy error.
- **Config:** See in-file comments in `gateway.config.yml`.

---

## Code/Config Documentation
- **In-file comments:** All major config and code files have comments for maintainers.
- **Auto-generated docs:**
  - For TypeScript code, consider using [TypeDoc](https://typedoc.org/):
    ```sh
    bun add -D typedoc
    npx typedoc src/
    ```
  - For YAML config, consider [doc-yaml](https://www.npmjs.com/package/doc-yaml) for generating Markdown docs from comments.

---

## Future Improvements
- Add more structured fields to logs (user ID, route, etc.)
- Integrate with external log aggregation/monitoring tools
- Add OpenAPI/Swagger docs for gateway endpoints if needed
- Expand this README as the gateway evolves

---

For more details, see in-file comments in the config and source files.

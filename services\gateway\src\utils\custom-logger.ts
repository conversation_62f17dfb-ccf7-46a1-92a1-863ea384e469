import { logger } from './logger';

export function createLogger() {
  return {
    log: (message: string, ...args: any[]) => logger.log('info', message, ...args), // For AJV (requires 'log' with level)
    info: (message: string, ...args: any[]) => logger.info(message, ...args), // For express-gateway's internal logger (requires 'info')
    warn: (message: string, ...args: any[]) => logger.warn(message, ...args), // Now using proper warn method
    error: (message: string, ...args: any[]) => logger.error(message, ...args),
    debug: (message: string, ...args: any[]) => logger.info(message, ...args), // Map debug to info
    verbose: (message: string, ...args: any[]) => logger.info(message, ...args), // Map verbose to info
    silly: (message: string, ...args: any[]) => logger.info(message, ...args), // Map silly to info
  };
}
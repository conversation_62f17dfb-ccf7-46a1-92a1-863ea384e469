# PETALSHEALTHAI Backend Services

This directory contains all the microservices that power the PETALSHEALTHAI platform backend. The architecture follows a microservices pattern with an API Gateway routing requests to specialized services.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                     API Gateway (Express Gateway)           │
│                         Port: 8080                          │
└─────────────────┬───────────────────────────┬───────────────┘
                  │                           │
    ┌─────────────▼────────────┐    ┌────────▼──────────────────┐
    │   Node.js Services       │    │   Python/FastAPI Services │
    │   (Express.js + Bun)     │    │   (FastAPI + UV)          │
    │                          │    │                           │
    │ • Auth Service (3001)    │    │ • AI Orchestrator (8000) │
    │ • User Service (3002)    │    │ • Analytics (8001)       │
    │ • Medical Records (3003) │    │ • Supplements (8002)     │
    │ • Chat Service (3004)    │    │ • Predictive Health      │
    │ • WhatsApp (3005)        │    │   (8003)                 │
    │ • Appointments (3006)    │    │ • Mental Health (8004)   │
    │ • Staff (3007)           │    │ • Emergency (8005)       │
    │ • Notifications (3008)   │    │ • Clinical Decision      │
    │ • Payment (3009)         │    │   (8006)                 │
    │ • Telemedicine (3010)    │    │                          │
    │ • Laboratory (3011)      │    │                          │
    │ • IoT Wearables (3012)   │    │                          │
    └──────────────────────────┘    └──────────────────────────┘
```

## 📋 Services Overview

### 🌐 API Gateway
- **Technology**: Express Gateway + Bun
- **Port**: 8080
- **Purpose**: Routes requests, handles CORS, rate limiting, and authentication validation
- **Key Features**: JWT validation, request routing, rate limiting, load balancing

### 🔐 Node.js Services (Express.js + Bun)

| Service | Port | Description |
|---------|------|-------------|
| **Auth Service** | 3001 | User/admin authentication, session management, JWT tokens, MFA |
| **User Service** | 3002 | User profile management, GDPR compliance, account lifecycle |
| **Medical Records** | 3003 | Secure medical data storage, HIPAA compliance, data export |
| **Chat Service** | 3004 | Real-time chat, WebSocket support, file uploads, emergency detection |
| **WhatsApp Service** | 3005 | WhatsApp Business API integration, multi-channel communication |
| **Appointment Service** | 3006 | Scheduling, availability management, calendar integration |
| **Staff Service** | 3007 | Healthcare staff management, role-based permissions |
| **Notification Service** | 3008 | Multi-channel notifications (email, SMS, push, WhatsApp) |
| **Payment Service** | 3009 | Payment processing, subscription billing, PCI compliance |
| **Telemedicine** | 3010 | Virtual consultation management, video integration |
| **Laboratory** | 3011 | Lab integration, results processing, order management |
| **IoT Wearables** | 3012 | Wearable device data processing, health metrics |

### 🐍 Python Services (FastAPI + UV)

| Service | Port | Description |
|---------|------|-------------|
| **AI Orchestrator** | 8000 | Manages AI agent swarms, LLM routing, conversation processing |
| **Analytics** | 8001 | Data analytics, compliance reporting, anonymization |
| **Supplement Optimization** | 8002 | AI-powered supplement recommendations |
| **Predictive Health** | 8003 | Health predictions, risk assessment, ML models |
| **Mental Health** | 8004 | Mental health assessments, crisis intervention |
| **Emergency Response** | 8005 | Emergency detection, crisis management, alerts |
| **Clinical Decision** | 8006 | Evidence-based clinical decision support |

## 🛠️ Tech Stack

### Node.js Services
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Package Manager**: Bun (for faster dependency management)
- **Database**: Supabase (PostgreSQL)
- **Cache/Sessions**: Redis
- **Authentication**: JWT + Supabase Auth
- **Real-time**: Socket.IO
- **File Storage**: Supabase Storage

### Python Services
- **Runtime**: Python 3.11+
- **Framework**: FastAPI
- **Package Manager**: UV (for faster dependency resolution)
- **Database**: Supabase (PostgreSQL) + SQLAlchemy
- **Cache**: Redis
- **AI/ML**: OpenAI, Anthropic, Google AI, LangChain
- **Data Science**: Pandas, NumPy, Scikit-learn

### Shared Infrastructure
- **Database**: Supabase (PostgreSQL with RLS)
- **Cache**: Redis
- **File Storage**: Supabase Storage
- **Monitoring**: Winston (Node.js), Structlog (Python)
- **API Gateway**: Express Gateway

## 🚀 Getting Started

### Prerequisites
1. **Node.js 18+** and **Bun** installed
2. **Python 3.11+** and **UV** installed
3. **Redis** server running
4. **Supabase** project set up
5. Environment variables configured (see `.env.example`)

### Installation

#### 1. Clone and Setup
```bash
# Navigate to services directory
cd services

# Install Bun (if not already installed)
curl -fsSL https://bun.sh/install | bash

# Install UV (if not already installed)
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### 2. Setup Individual Services

**Node.js Services (using Bun):**
```bash
# Example: Auth Service
cd auth-service
bun install
bun run dev

# Example: Chat Service  
cd ../chat-service
bun install
bun run dev
```

**Python Services (using UV):**
```bash
# Example: AI Orchestrator
cd ai-orchestrator-service
uv pip install -e .
uv run uvicorn app.main:app --reload

# Example: Analytics Service
cd ../analytics-service
uv pip install -e .
uv run uvicorn app.main:app --reload
```

#### 3. Start API Gateway
```bash
cd gateway
bun install
bun run dev
```

### 🔧 Development Workflow

#### Running Services in Development

**Start all Node.js services:**
```bash
# Use the provided development script
./scripts/start-node-services.sh
```

**Start all Python services:**
```bash
# Use the provided development script  
./scripts/start-python-services.sh
```

**Start API Gateway:**
```bash
cd gateway && bun run dev
```

#### Testing

**Node.js Services:**
```bash
cd [service-name]
bun test
bun test --watch
```

**Python Services:**
```bash
cd [service-name]
uv run pytest
uv run pytest --watch
```

## 🌍 Environment Configuration

### Required Environment Variables

Create a `.env` file in each service directory with the following variables:

```bash
# Core Application
NODE_ENV=development
PORT=[service-port]

# Database (Supabase)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Redis
REDIS_URL=redis://localhost:6379

# Security
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key

# External APIs
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
REQUESTLY_AI_API_KEY=your-requestly-key

# Communication Services
TWILIO_ACCOUNT_SID=your-twilio-sid
SENDGRID_API_KEY=your-sendgrid-key
WHATSAPP_ACCESS_TOKEN=your-whatsapp-token

# Payment
STRIPE_SECRET_KEY=your-stripe-key
```

See `./scripts/environment-setup-guide.md` for complete configuration details.

## 🔒 Security & Compliance

### Authentication & Authorization
- **JWT-based authentication** with refresh tokens
- **Multi-factor authentication** for admin accounts
- **Role-based access control (RBAC)** via Supabase RLS
- **Session management** with Redis
- **API rate limiting** via Express Gateway

### Data Protection
- **HIPAA compliance** for medical data
- **GDPR/NDPA compliance** for EU users
- **Encryption at rest** via Supabase
- **Encryption in transit** via TLS/HTTPS
- **Audit logging** with pgAudit
- **Data anonymization** for analytics

### Security Headers
- Helmet.js for Node.js services
- CORS configuration
- Content Security Policy
- HSTS enforcement

## 📊 Monitoring & Logging

### Logging
- **Node.js**: Winston with structured logging
- **Python**: Structlog with JSON formatting
- **Centralized logging** to files and console
- **Request/response logging** middleware

### Health Checks
Each service exposes a `/health` endpoint:
```bash
# Check service health
curl http://localhost:[port]/health
```

### Metrics
- Service-specific metrics via endpoints
- Performance monitoring
- Error rate tracking
- Usage analytics

## 🧪 Testing

### Test Structure
```
services/[service-name]/
├── tests/
│   ├── unit/          # Unit tests
│   ├── integration/   # Integration tests
│   ├── e2e/          # End-to-end tests
│   └── fixtures/     # Test data
```

### Running Tests
```bash
# Unit tests
bun test              # Node.js services
uv run pytest        # Python services

# Integration tests
bun test:integration  # Node.js services  
uv run pytest tests/integration/  # Python services

# Coverage
bun test:coverage     # Node.js services
uv run pytest --cov  # Python services
```

## 🚀 Deployment

### Production Considerations

1. **Environment Variables**: Use secure secret management
2. **Database**: Configure connection pooling and read replicas
3. **Redis**: Set up Redis cluster for high availability
4. **Load Balancing**: Deploy multiple instances behind load balancer
5. **SSL/TLS**: Enable HTTPS with valid certificates
6. **Monitoring**: Set up comprehensive monitoring and alerting

### Docker Support
Each service includes a Dockerfile for containerized deployment:

```bash
# Build and run a service
cd [service-name]
docker build -t petalshealthai/[service-name] .
docker run -p [port]:[port] petalshealthai/[service-name]
```

## 🔗 Inter-Service Communication

### Service Discovery
Services communicate via configured URLs in environment variables:

```javascript
// Node.js example
const authServiceUrl = process.env.AUTH_SERVICE_URL || 'http://localhost:3001';
```

```python
# Python example
auth_service_url = settings.auth_service_url
```

### API Contracts
- RESTful APIs with consistent response formats
- OpenAPI/Swagger documentation for FastAPI services
- Standardized error handling across services

## 📚 Additional Resources

- [API Gateway Configuration](./gateway/README.md)
- [Authentication Service Guide](./auth-service/README.md)
- [AI Orchestrator Documentation](./ai-orchestrator-service/README.md)
- [Environment Setup Guide](./scripts/environment-setup-guide.md)
- [Database Schema Documentation](./docs/database-schema.md)
- [Security Guidelines](./docs/security-guidelines.md)

## 🤝 Contributing

1. Follow the established patterns for each technology stack
2. Use the specified package managers (Bun for Node.js, UV for Python)
3. Include comprehensive tests for new features
4. Update documentation for API changes
5. Follow security best practices for healthcare data

## 📞 Support

For technical support or questions about the backend services:
- Review the service-specific README files
- Check the troubleshooting guides in `/docs`
- Contact the development team

---

**Built with ❤️ for healthcare innovation**

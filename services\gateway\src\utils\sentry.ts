// Modular Sentry utility for Gateway (opt-in via env)
// @ts-expect-error: Sentry types may be missing if @types/sentry__node is not installed
import * as Sentry from '@sentry/node';
import express from 'express';

let sentryEnabled = false;

export function initSentry() {
  if (sentryEnabled) return;
  if (process.env.SENTRY_DSN) {
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      tracesSampleRate: 1.0,
      environment: process.env.NODE_ENV || 'development',
    });
    sentryEnabled = true;
  }
}

export function sentryRequestHandler(): express.RequestHandler {
  if (sentryEnabled) {
    return Sentry.Handlers.requestHandler() as express.RequestHandler;
  }
  return (req, res, next) => next();
}

export function sentryErrorHandler(): express.ErrorRequestHandler {
  if (sentryEnabled) {
    return Sentry.Handlers.errorHandler() as express.ErrorRequestHandler;
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return (err, req, res, next) => next(err);
}

export function captureException(err: any) {
  if (sentryEnabled) {
    Sentry.captureException(err);
  }
} 
/**
 * PETALSHEALTHAI Laboratory Service
 * 
 * Handles lab integrations, results processing, and order management.
 * Port: 3011
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from 'dotenv';
import winston from 'winston';

config();

const app = express();
const PORT = process.env.LABORATORY_SERVICE_PORT || 3011;

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [ new winston.transports.Console() ],
});

app.use(helmet());
app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy', service: 'laboratory-service' });
});

app.use('/api/v1/lab', (req, res) => {
    res.status(501).json({ message: 'Not Implemented' });
});

app.listen(PORT, () => {
  logger.info(`Laboratory Service running on port ${PORT}`);
});

export default app;
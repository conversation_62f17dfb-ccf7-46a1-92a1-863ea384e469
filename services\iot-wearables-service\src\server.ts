/**
 * PETALSHEALTHAI IoT Wearables Service
 * 
 * Processes data from wearable devices and extracts health metrics.
 * Port: 3012
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from 'dotenv';
import winston from 'winston';

config();

const app = express();
const PORT = process.env.IOT_WEARABLES_SERVICE_PORT || 3012;

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [ new winston.transports.Console() ],
});

app.use(helmet());
app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy', service: 'iot-wearables-service' });
});

app.post('/api/v1/iot/data', (req, res) => {
    res.status(501).json({ message: 'Not Implemented' });
});

app.listen(PORT, () => {
  logger.info(`IoT Wearables Service running on port ${PORT}`);
});

export default app;
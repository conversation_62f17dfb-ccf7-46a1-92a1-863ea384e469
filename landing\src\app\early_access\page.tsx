"use client";

import { useState, useEffect, forwardRef } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Custom Input component for PhoneInput
const CustomPhoneInput = forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement>>((props, ref) => (
  <Input
    {...props}
    ref={ref}
    className="w-full bg-white border-gray-300 focus:border-[#6A8E99] focus:ring-[#6A8E99]"
  />
));
CustomPhoneInput.displayName = 'CustomPhoneInput';

// Success Message Component
const SuccessMessage = ({ message }: { message?: string }) => {
  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
      <Image 
        src="/images/icon-park_email-successfully.svg" 
        alt="Success" 
        width={120} 
        height={120} 
        className="mb-8"
      />
      <h2 className="text-2xl font-semibold mb-4 text-gray-800">
        Thanks for joining the Petals AI early access list.
      </h2>
      <p className="text-gray-600 max-w-md mx-auto mb-6">
        {message || "Keep an eye on your inbox for exclusive updates and beta invites"}
      </p>
      <p className="text-sm text-gray-500">
        Redirecting to home page in a few seconds...
      </p>
    </div>
  );
};

export default function EarlyAccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [responseMessage, setResponseMessage] = useState<string | undefined>(undefined);
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    role: "",
    phoneNumber: "",
  });
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Read query parameters on component mount
  useEffect(() => {
    const fullNameParam = searchParams.get('fullName');
    const emailParam = searchParams.get('email');
    
    // Update form data if query parameters exist
    setFormData(prevData => ({
      ...prevData,
      fullName: fullNameParam || prevData.fullName,
      email: emailParam || prevData.email
    }));
  }, [searchParams]);

  // Handle redirect after successful submission
  useEffect(() => {
    let redirectTimer: NodeJS.Timeout;
    
    if (isSubmitted) {
      redirectTimer = setTimeout(() => {
        router.push('/');
      }, 10000);
    }
    
    return () => {
      if (redirectTimer) clearTimeout(redirectTimer);
    };
  }, [isSubmitted, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleRoleChange = (value: string) => {
    setFormData((prev) => ({ ...prev, role: value }));
  };

  const handlePhoneChange = (value: string | undefined) => {
    setFormData((prev) => ({ ...prev, phoneNumber: value || "" }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);
    
    // Form validation
    if (!formData.fullName.trim()) {
      setSubmitError("Please enter your full name");
      return;
    }
    
    if (!formData.email.trim()) {
      setSubmitError("Please enter your email address");
      return;
    }
    
    if (!formData.role) {
      setSubmitError("Please select your role");
      return;
    }
    
    console.log("Form submitted:", formData);
    
    try {
      const response = await fetch('/api/early_access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      
      if (response.ok) {
        if (data.message) {
          // This means user already exists
          setResponseMessage(data.message);
        }
        setIsSubmitted(true);
      } else {
        setSubmitError(data.error || 'Failed to submit form. Please try again.');
        console.error('Error submitting form:', data);
      }
    } catch (error) {
      setSubmitError('An unexpected error occurred. Please try again.');
      console.error('Error submitting form:', error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#EAF7F9] py-10 px-4">
      <div className="max-w-md w-full mx-auto">
        <div className="mb-6">
          <Button variant="ghost" size="sm" asChild className="flex items-center gap-1 text-[#2E475D] hover:text-[#6A8E99] hover:bg-white/60">
            <Link href="/">
              <ChevronLeft className="h-4 w-4" />
              Back to Home
            </Link>
          </Button>
        </div>
        
        {isSubmitted ? (
          <SuccessMessage message={responseMessage} />
        ) : (
          <div className="rounded-xl p-6 md:p-8">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-semibold mb-3 text-[#2E475D]">
                Join the Petals AI Early Access List
              </h1>
              <p className="text-gray-600 max-w-sm mx-auto">
                Get early access to cutting-edge AI tools built to support your health or practice
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-5">
              {submitError && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-2 rounded-md text-sm">
                  {submitError}
                </div>
              )}
              
              <div className="space-y-2">
                <label htmlFor="fullName" className="block text-sm font-medium text-[#2E475D]">
                  Full name
                </label>
                <Input
                  id="fullName"
                  name="fullName"
                  placeholder="Enter your name"
                  value={formData.fullName}
                  onChange={handleChange}
                  required
                  className="w-full bg-white border-gray-300 focus:border-[#6A8E99] focus:ring-[#6A8E99]"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium text-[#2E475D]">
                  Email Address
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full bg-white border-gray-300 focus:border-[#6A8E99] focus:ring-[#6A8E99]"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="role" className="block text-sm font-medium text-[#2E475D]">
                  Role Selection
                </label>
                <Select onValueChange={handleRoleChange} value={formData.role}>
                  <SelectTrigger className="w-full bg-white border-gray-300 text-gray-700">
                    <SelectValue placeholder="I'm a Provider" />
                  </SelectTrigger>
                  <SelectContent className="bg-white">
                    <SelectItem value="provider">I'm a Provider</SelectItem>
                    <SelectItem value="patient">I'm a Patient</SelectItem>
                    <SelectItem value="administrator">I'm an Administrator</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-[#2E475D]">
                  Phone Number
                </label>
                <PhoneInput
                  international
                  countryCallingCodeEditable={true}
                  defaultCountry="US"
                  value={formData.phoneNumber}
                  onChange={handlePhoneChange}
                  inputComponent={CustomPhoneInput}
                />
              </div>

              <Button
                type="submit"
                className="w-full py-2.5 bg-[#7da0a9] hover:bg-[#6b8f99] text-white font-medium rounded-md mt-4"
              >
                Join Early Access
              </Button>

              <p className="text-center text-sm text-gray-600 mt-4">
                We'll notify you first when Petals AI is ready for you
              </p>
            </form>
          </div>
        )}
      </div>
    </div>
  );
}

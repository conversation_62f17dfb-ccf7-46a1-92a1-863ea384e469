---
description: Reference for Taskmaster MCP tools and CLI commands
globs: **/*
alwaysApply: true
---

# Taskmaster Reference

This document provides a comprehensive reference for Taskmaster MCP tools and CLI commands.

## Initialization

### `init` / `task-master init`

Initializes a new Taskmaster project.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "init",
  args: {
    // Optional parameters
    projectName: "My Project",  // Default: derived from directory
    description: "Project description",  // Default: empty
    outputDir: "./tasks"  // Default: ./tasks
  }
});
```

**CLI Command:**
```bash
task-master init [--project-name="My Project"] [--description="Project description"] [--output-dir="./tasks"]
```

### `parse_prd` / `task-master parse-prd`

Parses a PRD document to generate initial tasks.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "parse_prd",
  args: {
    // Required parameters
    input: "PRD content or file path",
    
    // Optional parameters
    outputDir: "./tasks",  // Default: ./tasks
    format: "markdown"  // Default: markdown (alternatives: text, json)
  }
});
```

**CLI Command:**
```bash
task-master parse-prd --input="path/to/prd.md" [--output-dir="./tasks"] [--format="markdown"]
```

## Task Listing and Viewing

### `get_tasks` / `task-master list`

Lists all tasks with their status.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "get_tasks",
  args: {
    // Optional parameters
    status: "pending",  // Filter by status: pending, done, deferred, or all
    format: "table"  // Output format: table, json, or markdown
  }
});
```

**CLI Command:**
```bash
task-master list [--status="pending"] [--format="table"]
```

### `next_task` / `task-master next`

Identifies the next task to work on based on dependencies and priority.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "next_task",
  args: {}
});
```

**CLI Command:**
```bash
task-master next
```

### `get_task` / `task-master show`

Displays detailed information about a specific task.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "get_task",
  args: {
    // Required parameters
    id: "1.2"  // Task ID
  }
});
```

**CLI Command:**
```bash
task-master show <id>
```

## Task Creation and Modification

### `add_task` / `task-master add-task`

Adds a new task to the project.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "add_task",
  args: {
    // Required parameters for simple creation
    title: "Task title",
    
    // Alternative: use prompt for AI-assisted creation
    prompt: "Create a task for implementing user authentication",
    
    // Optional parameters
    description: "Task description",
    status: "pending",  // Default: pending
    priority: "medium",  // Default: medium
    dependencies: ["1", "2.3"],  // Default: []
    details: "Implementation details",
    testStrategy: "Testing approach"
  }
});
```

**CLI Command:**
```bash
# Simple creation
task-master add-task --title="Task title" [--description="Task description"] [--status="pending"] [--priority="medium"] [--dependencies="1,2.3"] [--details="Implementation details"] [--test-strategy="Testing approach"]

# AI-assisted creation
task-master add-task --prompt="Create a task for implementing user authentication"
```

### `update_task` / `task-master update-task`

Updates an existing task.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "update_task",
  args: {
    // Required parameters
    id: "1.2",
    
    // Use either direct field updates or prompt
    // Direct field updates
    title: "Updated title",
    description: "Updated description",
    status: "done",
    priority: "high",
    dependencies: ["1", "3.1"],
    details: "Updated implementation details",
    testStrategy: "Updated testing approach",
    
    // Or use prompt for AI-assisted update
    prompt: "Update this task to include error handling requirements"
  }
});
```

**CLI Command:**
```bash
# Direct field updates
task-master update-task --id="1.2" [--title="Updated title"] [--description="Updated description"] [--status="done"] [--priority="high"] [--dependencies="1,3.1"] [--details="Updated implementation details"] [--test-strategy="Updated testing approach"]

# AI-assisted update
task-master update-task --id="1.2" --prompt="Update this task to include error handling requirements"
```

### `set_task_status` / `task-master set-status`

Updates the status of a task.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "set_task_status",
  args: {
    // Required parameters
    id: "1.2",
    status: "done"  // pending, done, deferred, or custom status
  }
});
```

**CLI Command:**
```bash
task-master set-status --id="1.2" --status="done"
```

### `add_subtask` / `task-master add-subtask`

Adds a subtask to an existing task.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "add_subtask",
  args: {
    // Required parameters
    parent: "1",  // Parent task ID
    title: "Subtask title",
    
    // Optional parameters
    description: "Subtask description",
    status: "pending",  // Default: pending
    details: "Implementation details"
  }
});
```

**CLI Command:**
```bash
task-master add-subtask --parent="1" --title="Subtask title" [--description="Subtask description"] [--status="pending"] [--details="Implementation details"]
```

### `update_subtask` / `task-master update-subtask`

Updates an existing subtask.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "update_subtask",
  args: {
    // Required parameters
    id: "1.2",  // Subtask ID
    
    // Optional parameters for direct updates
    title: "Updated subtask title",
    description: "Updated subtask description",
    status: "done",
    details: "Updated implementation details",
    
    // Or use prompt for AI-assisted update
    prompt: "Update this subtask to include validation requirements"
  }
});
```

**CLI Command:**
```bash
# Direct field updates
task-master update-subtask --id="1.2" [--title="Updated subtask title"] [--description="Updated subtask description"] [--status="done"] [--details="Updated implementation details"]

# AI-assisted update
task-master update-subtask --id="1.2" --prompt="Update this subtask to include validation requirements"
```

### `clear_subtasks` / `task-master clear-subtasks`

Removes all subtasks from a task.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "clear_subtasks",
  args: {
    // Required parameters
    id: "1"  // Task ID
  }
});
```

**CLI Command:**
```bash
task-master clear-subtasks --id="1"
```

### `add_dependency` / `task-master add-dependency`

Adds a dependency to a task.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "add_dependency",
  args: {
    // Required parameters
    id: "2.1",  // Task ID
    dependency: "1.3"  // Dependency task ID
  }
});
```

**CLI Command:**
```bash
task-master add-dependency --id="2.1" --dependency="1.3"
```

### `remove_dependency` / `task-master remove-dependency`

Removes a dependency from a task.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "remove_dependency",
  args: {
    // Required parameters
    id: "2.1",  // Task ID
    dependency: "1.3"  // Dependency task ID to remove
  }
});
```

**CLI Command:**
```bash
task-master remove-dependency --id="2.1" --dependency="1.3"
```

### `validate_dependencies` / `task-master validate-dependencies`

Checks for circular dependencies and other issues.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "validate_dependencies",
  args: {}
});
```

**CLI Command:**
```bash
task-master validate-dependencies
```

### `fix_dependencies` / `task-master fix-dependencies`

Attempts to automatically fix dependency issues.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "fix_dependencies",
  args: {}
});
```

**CLI Command:**
```bash
task-master fix-dependencies
```

## Task Analysis and Expansion

### `analyze_complexity` / `task-master analyze-complexity`

Analyzes the complexity of tasks.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "analyze_complexity",
  args: {
    // Optional parameters
    id: "1",  // Specific task ID to analyze, omit for all pending tasks
    research: true,  // Whether to use Perplexity AI for research-backed analysis
    outputFile: "complexity-report.json"  // Default: complexity-report.json
  }
});
```

**CLI Command:**
```bash
task-master analyze-complexity [--id="1"] [--research] [--output-file="complexity-report.json"]
```

### `complexity_report` / `task-master complexity-report`

Displays the complexity analysis report.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "complexity_report",
  args: {
    // Optional parameters
    format: "table",  // Output format: table, json, or markdown
    inputFile: "complexity-report.json"  // Default: complexity-report.json
  }
});
```

**CLI Command:**
```bash
task-master complexity-report [--format="table"] [--input-file="complexity-report.json"]
```

### `expand_task` / `task-master expand`

Breaks down a task into subtasks.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "expand_task",
  args: {
    // Required parameters
    id: "1",  // Task ID to expand
    
    // Optional parameters
    num: 5,  // Number of subtasks to generate (default: based on complexity)
    research: true,  // Whether to use Perplexity AI for research-backed expansion
    prompt: "Additional context for expansion",  // Additional context
    complexityReport: "complexity-report.json"  // Default: complexity-report.json
  }
});
```

**CLI Command:**
```bash
task-master expand --id="1" [--num=5] [--research] [--prompt="Additional context for expansion"] [--complexity-report="complexity-report.json"]
```

### `expand_all` / `task-master expand --all`

Expands all pending tasks.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "expand_all",
  args: {
    // Optional parameters
    research: true,  // Whether to use Perplexity AI for research-backed expansion
    complexityReport: "complexity-report.json"  // Default: complexity-report.json
  }
});
```

**CLI Command:**
```bash
task-master expand --all [--research] [--complexity-report="complexity-report.json"]
```

## Task File Management

### `generate` / `task-master generate`

Generates task files from tasks.json.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "generate",
  args: {
    // Optional parameters
    outputDir: "./tasks",  // Default: ./tasks
    format: "markdown"  // Default: markdown (alternatives: text, json)
  }
});
```

**CLI Command:**
```bash
task-master generate [--output-dir="./tasks"] [--format="markdown"]
```

### `update` / `task-master update`

Updates future tasks based on current implementation.

**MCP Tool:**
```javascript
run_mcp({
  server_name: "mcp.config.usrlocalmcp.taskmaster",
  tool_name: "update",
  args: {
    // Required parameters
    from: "1",  // Task ID that was implemented differently
    prompt: "Explain how implementation differs and what needs to change"
  }
});
```

**CLI Command:**
```bash
task-master update --from="1" --prompt="Explain how implementation differs and what needs to change"
```
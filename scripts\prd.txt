# Product Requirements Document (PRD) - PETALSHEALTHAI Backend (Consolidated)

**Version:** 2.0 (Updated with Cross-Channel Data Management Agent System)
**Date:** December 19, 2024

## 1. Introduction

### 1.1. Project Goal
To build a robust, scalable, secure, and compliant backend system for the PETALSHEALTHAI telemedicine web application. This backend will utilize Supabase as the primary database and PostgreSQL instance, leverage Express Gateway as the API Gateway, and power an AI-driven conversational agent swarm. Key functionalities include providing health-related information/suggestions, managing user and administrative accounts, securely handling medical records, integrating with external health data sources, and ensuring adherence to relevant data protection regulations (HIPAA, GDPR, NDPA, MDR, ePrivacy).

### 1.2. Target Audience
* **End Users:** Individuals seeking health information, suggestions, and management of their health data via the web application.
* **Administrators:** Personnel responsible for managing the application, users, content (if applicable backend-side), compliance, and monitoring system health and security.
* **Healthcare Providers (Indirect):** May receive exported medical records shared by users.

### 1.3. Scope

#### 1.3.1. In Scope (Backend ONLY)
* **API Gateway Configuration (Express Gateway):** Defining routes, policies (rate limiting, basic auth checks if applicable), and integration points for backend services.
* **Microservices Development:**
    * User Service (Node.js/Express.js with **Bun** package manager)
    * Authentication Service (Node.js/Express.js with **Bun** package manager - User & Admin + Centralized Session Management)
    * Medical Record Management Service (Node.js/Express.js with **Bun** package manager)
    * Chat Service (Node.js/Express.js with **Bun** package manager - including WebSocket support if needed)
    * WhatsApp Service (Node.js/Express.js with **Bun** package manager - WhatsApp Business API integration)
    * Appointment Service (Node.js/Express.js with **Bun** package manager - basic structure)
    * Staff Service (Node.js/Express.js with **Bun** package manager)
    * Payment Service (Node.js/Express.js with **Bun** package manager)
    * Notification Service (Node.js/Express.js with **Bun** package manager)
    * **Supplement Optimization Service (FastAPI with UV package manager - NEW)**
    * **Predictive Health Service (FastAPI with UV package manager - NEW)**
    * **Mental Health Service (FastAPI with UV package manager - NEW)**
    * **Telemedicine Service (Node.js/Express.js with Bun package manager - NEW)**
    * **Laboratory Integration Service (Node.js/Express.js with Bun package manager - NEW)**
    * **Emergency Response Service (FastAPI with UV package manager - NEW)**
    * **Clinical Decision Support Service (FastAPI with UV package manager - NEW)**
    * **IoT/Wearables Integration Service (Node.js/Express.js with Bun package manager - NEW)**
    * AI Orchestrator Service (FastAPI with **UV** package manager)
    * Analytics Service (FastAPI with **UV** package manager)
* **AI Agent Core Logic:** Conversation flow, NLP, state management.
* **AI Agent Swarm:** Orchestration (FastAPI, potentially ADK), specialized agent logic (research, data retrieval, suggestion generation), inter-agent communication.
* **User & Admin Management:** Registration, profiles, authentication (Supabase Auth), authorization (RBAC/RLS via Supabase), account lifecycle management.
* **Medical Record Management:** Creation (via chat parsing), secure storage (Supabase), retrieval, export/sharing features.
* **Database Architecture & Implementation:** PostgreSQL schema design within Supabase, RLS policy implementation, indexing, backups.
* **External API Integrations:** Connecting to MedlinePlus, Exercise APIs, Air Quality APIs, FoodData Central, Recipe Sources (Scraping/APIs).
* **Security Implementation:** Encryption (at-rest, in-transit via Supabase), Access Control (RBAC/RLS via Supabase), Audit Logging (pgAudit on Supabase), secure coding practices, API security.
* **Compliance Implementation:** Backend logic and infrastructure to meet GDPR, MDR, ePrivacy, HIPAA, and NDPA requirements.
* **LLM Access Management:** Integration of Requestly AI API key router within the FastAPI orchestrator.

#### 1.3.2. Development Tools & Package Managers
* **Node.js/Express.js Services:** All Node.js-based microservices will use **Bun** as the package manager for faster dependency management, improved performance, and modern JavaScript runtime capabilities.
* **FastAPI Services:** All Python-based services will use **UV** as the package manager for faster dependency resolution, better virtual environment management, and improved Python package installation performance.
* **API Gateway:** Express Gateway will be configured and managed using **Bun** for consistency with other Node.js services.

#### 1.3.3. Out of Scope
* Frontend User Interface (UI) development (Landing pages, User Dashboard, Admin Portal UI).
* Frontend User Experience (UX) design.
* Client-side logic residing in the web browser.
* Deployment infrastructure setup beyond basic configuration guidance (specific IaC scripts, K8s manifests etc. are implementation details).
* Payment Service implementation details (placeholder service defined).
* Detailed content population for medical information databases.

## 2. Core Functional Requirements

### 2.1. User Management

* **User Registration:** Allow new users to register via the `/api/users/register` endpoint. Store user details securely in Supabase.
* **User Profiles:** Provide endpoints (`/api/users/me`) for users to retrieve and update their profile information (excluding sensitive medical data, which is managed separately).
* **User Deletion:** Provide an endpoint (`/api/users/me`) for users to request account deletion, triggering processes compliant with data erasure rights under GDPR/NDPA (anonymization or deletion of associated records, conversations, etc., subject to retention policies).

### 2.2. Administrative Management

* **Admin Account Lifecycle:**
    * Define a secure, documented process for creating the initial 'Super Admin' account(s).
    * Implement secure procedures within an admin-only interface/API (`/api/admin/users/`) for provisioning new administrative accounts and assigning appropriate roles (e.g., initiated by a Super Admin).
    * Implement secure procedures for promptly deactivating or deleting administrative accounts and revoking all associated privileges (via `/api/admin/users/{userId}`) when personnel leave or change roles.
    * Mandate periodic (e.g., quarterly) review and recertification of all active administrative accounts and their assigned permissions.
* **Admin Roles & Permissions:**
    * Define a minimal set of administrative roles based on the principle of least privilege (See Table 1: Proposed Admin Roles).
    * Permissions must be assigned based *only* on these defined roles.
    * Implement Role-Based Access Control (RBAC) using Supabase features (See Section 2.4).

    **Table 1: Proposed Admin Roles and Core Backend Permissions (Illustrative - Refine based on exact needs)**
    | Role Name          | Description                                                                 | Core Backend Responsibilities & Example Permissions (via API/RLS)                                                                                                                                                                                                                                                           |
    | :----------------- | :-------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
    | **Super Admin** | Highest level access; manages system settings, users, roles. Limited count. | Overall system configuration (via specific endpoints), User Management (CRUD on `/api/users/`, `/api/admin/users/`), Role/Permission Management (CRUD on `/api/admin/roles/`, `/api/admin/permissions/`), Critical Data Operations (e.g., potential bulk deletion if needed, with strict controls), Audit Log Access (`/api/admin/audit-logs/`). |
    | **Content Manager**| Manages application content (if backend manages templates/static info).     | CRUD operations on content-related endpoints/tables (e.g., `/api/admin/content/`).                                                                                                                                                                                                                                           |
    | **User Manager** | Manages end-user accounts and non-sensitive data.                           | Read/Update (limited fields) on `/api/users/`, Suspend/Unsuspend users (`/api/admin/users/{userId}/status`), potentially trigger password resets. **Cannot** access sensitive medical records directly unless explicitly granted via RLS for specific support tasks (highly restricted).                                     |
    | **Compliance Auditor** | Read-only access for monitoring and auditing compliance.                    | Read-only access to Audit Logs (`/api/admin/audit-logs/`), Read-only access to user lists/profiles (limited fields via `/api/users/` or `/api/admin/users/`), Read-only access to system configurations (`/api/admin/settings/`), Read-only access to content (`/api/admin/content/`). **Strictly no modification rights.** |

### 2.3. Authentication & Session Management (User & Admin)

* **User Authentication:**
    * Implement user login (`/api/auth/login`) using Supabase Auth.
    * Issue JWTs upon successful login.
    * Provide endpoints for logout (`/api/auth/logout`), token refresh (`/api/auth/refresh-token`), and password reset (`/api/auth/request-password-reset`, `/api/auth/reset-password`).
* **Admin Authentication:**
    * **Mechanism:** Specify the chosen method (e.g., Dedicated Admin Login Endpoint `/api/admin/auth/login`, Role-based access via main `/api/auth/login` with post-login role check, or SSO integration via Supabase Auth).
    * **MFA:** Mandate Multi-Factor Authentication (MFA) for *all* administrative authentication attempts. Specify acceptable second factors (e.g., TOTP authenticator apps). Enforce via Supabase Auth settings or custom logic.
    * **Password Policies:** Enforce strong password policies for admin accounts (min. 12-15 characters, complexity encouragement, check against breached lists). Configure via Supabase Auth settings or backend validation. Implement account lockout policies.
    * **Secure Recovery:** Define an exceptionally secure process for admin account recovery.
* **Centralized Session Management (Authentication Service):**
    * **Session Creation:** Upon successful login, create a session record containing session ID, user ID, role, device fingerprint, IP address, login timestamp, and expiration time. Store in Redis for fast access and Supabase for persistence.
    * **Session Validation:** The Authentication Service serves as the authoritative source for active session validation. API Gateway calls Authentication Service (`/api/auth/validate-session`) for every protected request before routing to microservices.
    * **Session Metadata Tracking:** Maintain comprehensive session data including last activity timestamp, device information, geolocation (if consented), and concurrent session count per user.
    * **Session Refresh:** Handle token refresh (`/api/auth/refresh-token`) while extending session expiration and updating last activity. Support sliding session windows for active users.
    * **Session Termination:** Implement multiple termination scenarios:
        - User logout (`/api/auth/logout`) - terminates current session
        - Global logout (`/api/auth/logout-all`) - terminates all user sessions across devices
        - Administrative session revocation (`/api/admin/users/{userId}/revoke-sessions`)
        - Automatic timeout based on inactivity
        - Security-triggered revocation (suspicious activity, password change, role modification)
    * **Session Monitoring:** Track concurrent sessions per user, detect anomalous login patterns, and implement session-based rate limiting. Generate alerts for suspicious session activity.
    * **Session Storage Architecture:**
        - **Redis:** Primary session store for fast validation (TTL-based expiration)
        - **Supabase:** Persistent session history for audit trails and analytics
        - **Session Table Schema:** `sessions(id, user_id, jwt_token_hash, device_fingerprint, ip_address, user_agent, created_at, last_activity, expires_at, is_active, revoked_at, revoked_reason)`
* **JWT Validation & Session Integration:** All protected backend API endpoints receive pre-validated requests from the API Gateway. The Gateway validates both JWT structure/signature AND active session status via the Authentication Service before routing requests. Individual microservices can trust the Gateway's validation and focus on business logic.

### 2.4. Authorization (RBAC & RLS via Supabase)

* **Implementation Strategy:** Specify the chosen technical approach for RBAC within Supabase (e.g., "Administrative and User RBAC will be implemented using Custom Claims injected into the JWT via a Supabase Custom Access Token Auth Hook. Roles and permissions will be stored in dedicated database tables (`roles`, `permissions`, `user_roles`). RLS policies will check these JWT claims (`auth.jwt() -> 'app_metadata' ->> 'user_role'`)"). *[Adjust based on chosen strategy from Admin PRD analysis: Custom Claims, Postgres Roles, Direct RLS Checks]*
* **Row Level Security (RLS):**
    * Mandate the enablement of RLS on *all* Supabase database tables containing sensitive data (e.g., `medical_records`, `conversations`, `users` table personal info) and tables involved in administrative functions.
    * RLS policies *must* be created for these tables to enforce access control based strictly on:
        * **User Access:** Authenticated users can only access/modify their *own* data (e.g., `USING (auth.uid() = user_id)`).
        * **Admin Access:** Access is granted based on the defined administrative roles and permissions, utilizing the chosen implementation mechanism (checking JWT claims, role membership, or helper functions). Policies must adhere to the principle of least privilege defined in Table 1.
* **`service_role` Key Security:** The Supabase `service_role` key must *never* be exposed client-side. It should only be used in secure backend environments (e.g., specific FastAPI admin endpoints, Supabase Edge Functions) *after* verifying the caller is an authenticated administrator with the necessary permissions for the privileged action (e.g., using `supabase.auth.admin` methods like user deletion).

### 2.5. AI Conversational Agent & Swarm

**Overview:**  
The AI Conversational Agent system consists of two primary components: the Chat Service (Node.js/Express.js) for real-time conversation management and the AI Orchestrator Service (FastAPI) for coordinating specialized AI agent swarms to provide intelligent health information and suggestions.

## 2.5.1. Chat Service (Node.js/Express.js)

**Core Functionality:**
* **Real-Time Communication Infrastructure:**
    * WebSocket implementation using Socket.IO for instant message delivery
    * Connection management with authentication and session validation
    * Auto-reconnection handling for unstable network conditions
    * Multi-room support for different conversation contexts
    * Connection scaling across multiple server instances using Redis adapter

* **Message Processing & Threading:**
    * Message threading system to maintain conversation context across long interactions
    * Conversation state management with Redis-based session storage
    * Message queuing for reliable delivery during high traffic
    * Message history pagination and search functionality
    * Cross-channel message synchronization (web ↔ WhatsApp)

* **File Upload & Media Handling:**
    * Secure file upload for medical documents, images, and lab results
    * File type validation and virus scanning using ClamAV
    * Image compression and format standardization
    * File storage in Supabase with encrypted metadata
    * Size limits and quota management per user tier
    * Medical document OCR for text extraction and analysis

* **Real-Time Features:**
    * Typing indicators with multi-user support
    * Read receipts and message delivery status
    * Online/offline status tracking
    * Voice message recording and playback support
    * Live conversation monitoring for staff escalation

* **Emergency Escalation System:**
    * Keyword detection for urgent medical queries ("emergency", "chest pain", "suicide")
    * Automatic staff notification for critical situations
    * Priority message routing to available medical professionals
    * Emergency contact integration and notification
    * Crisis intervention protocol activation

* **Rate Limiting & Abuse Prevention:**
    * User-based message rate limiting (configurable per minute/hour)
    * Spam detection using pattern recognition
    * Automated temporary bans for abusive behavior
    * Content filtering for inappropriate language
    * Bot detection and prevention mechanisms

**API Endpoints:**
* `/api/v1/conversations/` (GET, POST - List and create conversations)
* `/api/v1/conversations/{conversationId}` (GET - Get conversation history)
* `/api/v1/conversations/{conversationId}/messages` (POST - Send message)
* `/api/v1/conversations/{conversationId}/search` (GET - Search messages)
* `/api/v1/conversations/{conversationId}/export` (POST - Export conversation)
* `/api/v1/conversations/upload` (POST - Upload files)
* `/api/v1/conversations/media/{fileId}` (GET - Retrieve media files)
* `/ws/v1/conversations/` (WebSocket - Real-time communication)

**Database Schema Extensions:**
```sql
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    staff_id UUID REFERENCES users(id), -- NULL for AI-only conversations
    channel VARCHAR(20) DEFAULT 'web', -- 'web', 'whatsapp'
    status VARCHAR(50) DEFAULT 'active', -- 'active', 'archived', 'escalated'
    priority VARCHAR(20) DEFAULT 'normal', -- 'low', 'normal', 'urgent', 'emergency'
    last_message_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    archived_at TIMESTAMP
);

CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES conversations(id),
    sender_id UUID REFERENCES users(id), -- NULL for AI messages
    sender_type VARCHAR(20), -- 'user', 'ai', 'staff'
    content TEXT,
    message_type VARCHAR(50) DEFAULT 'text', -- 'text', 'image', 'document', 'voice', 'system'
    metadata JSONB, -- File info, AI context, etc.
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE conversation_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES conversations(id),
    user_id UUID REFERENCES users(id),
    role VARCHAR(20), -- 'patient', 'doctor', 'nurse', 'observer'
    joined_at TIMESTAMP DEFAULT NOW(),
    left_at TIMESTAMP
);

CREATE TABLE message_attachments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID REFERENCES messages(id),
    file_name VARCHAR(255),
    file_type VARCHAR(100),
    file_size BIGINT,
    storage_path TEXT,
    is_processed BOOLEAN DEFAULT FALSE,
    ocr_text TEXT, -- Extracted text from images/documents
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 2.5.2. AI Orchestrator Service (FastAPI)

**Core Architecture:**
* **Agent Swarm Management:**
    * Orchestration layer using FastAPI with async/await for high concurrency
    * Specialized agent definitions with clear responsibilities and interfaces
    * Load balancing across multiple agent instances for scalability
    * Health monitoring and automatic failover for individual agents
    * Dynamic agent scaling based on request volume and complexity

* **Specialized Agent Types:**
    
    **Core Medical Agents:**
    * **Research Agent:** Medical literature search, condition information retrieval
    * **Data Retrieval Agent:** External API integration (MedlinePlus, exercise, nutrition)
    * **Analysis Agent:** Medical document and image analysis
    * **Context Agent:** Conversation history analysis and context preservation
    
    **Clinical Decision Support Agents:**
    * **Symptom Contextualizer Agent:** Analyzes symptoms in context of medical history, medications, lifestyle
    * **Diagnostic Assistant Agent:** Helps with differential diagnosis and clinical reasoning
    * **Treatment Recommendation Agent:** Suggests evidence-based treatment options
    * **Clinical Decision Support Agent:** Evidence-based medical decision assistance
    * **Risk Assessment Agent:** Calculates health risks and predictive scoring
    
    **Safety & Drug Management Agents:**
    * **Drug Interaction Agent:** Pharmaceutical safety checks and contraindication detection
    * **Supplement Interaction Agent:** Supplement/drug interaction analysis and safety
    * **Medication Adherence Agent:** Tracks and improves medication compliance
    * **Emergency Triage Agent:** Triages emergency situations and crisis intervention
    
    **Specialized Health Agents:**
    * **Mental Health Assessment Agent:** Specialized mental health evaluation and support
    * **Predictive Health Agent:** ML-based health predictions and trend analysis
    * **Lifestyle Optimization Agent:** Diet, exercise, sleep, and wellness recommendations
    * **Lab Results Interpreter Agent:** Analyzes and explains laboratory results
    * **Patient Education Agent:** Creates personalized educational content
    
    **Operational Support Agents:**
    * **Insurance Pre-authorization Agent:** Handles insurance workflows and pre-approvals
    * **Scraping Agent:** Ethical web scraping for nutrition and health resources
    * **Suggestion Agent:** Personalized health recommendations based on user data

* **Inter-Agent Communication:**
    * Standardized Pydantic models for all agent communications
    * Internal REST API calls between orchestrator and agents
    * Message passing using Redis for complex workflows
    * Context sharing and state synchronization across agents
    * Error propagation and fallback handling

* **LLM Integration & Management:**
    * Requestly AI API key router integration for cost optimization
    * Multiple LLM provider support (OpenAI, Anthropic, Google, local models)
    * Request routing based on query complexity and cost considerations
    * Response caching to reduce API calls and improve performance
    * Usage tracking and cost monitoring per user and query type

* **Context Management & Memory:**
    * Conversation context preservation across agent handoffs
    * User profile integration for personalized responses
    * Medical history awareness for contextual suggestions
    * Short-term memory for conversation flow
    * Long-term memory for user preferences and patterns

* **Response Quality & Safety:**
    * Multi-layer response validation and safety checking
    * Medical disclaimer injection for health-related responses
    * Fact-checking against reliable medical sources
    * Bias detection and mitigation in AI responses
    * Response quality scoring and feedback integration

**API Endpoints:**
* `/api/v1/ai/process` (POST - Process user query through agent swarm)
* `/api/v1/ai/agents/` (GET - List available agents and their status)
* `/api/v1/ai/agents/{agentId}/health` (GET - Check agent health)
* `/api/v1/ai/context/{userId}` (GET, PUT - Manage user context)
* `/api/v1/ai/feedback` (POST - Submit response quality feedback)
* `/api/v1/ai/analytics` (GET - AI performance metrics)
* `/api/v1/ai/models/` (GET - Available LLM models and usage)
* `/api/v1/ai/cache/stats` (GET - Cache performance statistics)

**Agent Communication Schema:**
```python
# Pydantic models for agent communication
class AgentRequest(BaseModel):
    query: str
    context: Dict[str, Any]
    user_id: str
    conversation_id: str
    priority: Literal["low", "normal", "urgent", "emergency"]
    max_response_time: int = 30  # seconds

class AgentResponse(BaseModel):
    agent_id: str
    response: str
    confidence: float
    sources: List[str]
    metadata: Dict[str, Any]
    processing_time: float
    error: Optional[str] = None

class OrchestratorContext(BaseModel):
    user_profile: Dict[str, Any]
    conversation_history: List[Dict[str, Any]]
    medical_context: Dict[str, Any]
    preferences: Dict[str, Any]
    active_agents: List[str]
```

**Performance Monitoring:**
* Agent response time tracking and optimization
* Load balancing effectiveness monitoring
* LLM usage and cost analysis
* Cache hit rates and performance metrics
* User satisfaction and response quality tracking
* Error rates and failure pattern analysis

**Integration Points:**
* **Chat Service:** Receives queries and delivers AI responses
* **Medical Records Service:** Accesses user medical history for context
* **User Service:** Retrieves user preferences and profile information
* **Analytics Service:** Sends performance and usage metrics
* **External APIs:** Coordinates data retrieval from medical and health sources

### 2.6. Cross-Channel Data Management Agent System

**Overview:**  
The Data Management Agent System provides intelligent, automated processing of medical data from both web chat and WhatsApp channels while maintaining unified data integrity, emergency detection, and smart escalation capabilities. This system operates silently in the background without direct user interaction.

**Core Architecture Principle:**
✅ **Separate but Unified**: Web chat and WhatsApp maintain independent conversation flows while sharing a unified data layer managed by intelligent background agents.

```
┌─────────────────┐    ┌─────────────────┐
│   Web Chat      │    │   WhatsApp      │
│   (Multi-conv)  │    │   (Single conv) │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     ▼
┌─────────────────────────────────────────┐
│        UNIFIED DATA LAYER               │
│  • Medical Records                      │
│  • User Context                         │
│  • Conversation History                 │
└─────────────────┬───────────────────────┘
                  ▼
┌─────────────────────────────────────────┐
│     DATA MANAGEMENT AGENT SYSTEM       │
│  • General Purpose Medical Analyzer    │
│  • Emergency Detection                  │
│  • Conflict Resolution                  │
│  • Smart Escalation                    │
└─────────────────────────────────────────┘
```

## 2.6.1. Data Management Agent Core Functionality

**General Purpose Medical Analysis:**
* **Objective Analysis**: No symptom-focused prompting - analyzes any medical input objectively
* **Data Extraction**: Automatically identifies and extracts medical information from natural language
* **Confidence Scoring**: Quantifies certainty of extracted data for decision-making
* **Conflict Detection**: Identifies inconsistencies between new and existing data
* **Temporal Understanding**: Recognizes time-based medical information and relationships

**Agent Processing Logic:**
```python
class MedicalDataAgent:
    def analyze_input(self, raw_text, user_context, timestamp, channel):
        analysis = {
            'medical_data_extracted': self.extract_medical_info(raw_text),
            'urgency_level': self.assess_urgency(raw_text),
            'data_conflicts': self.detect_conflicts(user_context),
            'confidence_score': self.calculate_confidence(),
            'recommended_action': self.determine_action(),
            'requires_escalation': self.check_escalation_criteria()
        }
        return analysis
```

**Confidence Level Framework:**
```python
CONFIDENCE_THRESHOLDS = {
    'AUTO_PROCESS': 0.90,      # High confidence - process automatically
    'STAFF_REVIEW': 0.70,      # Medium confidence - flag for staff review
    'ADMIN_ESCALATION': 0.50,  # Low confidence - escalate to admin
    'EMERGENCY_ALERT': None    # Any emergency triggers immediate alert
}
```

## 2.6.2. Emergency Detection & Notification System

**Emergency Detection Capabilities:**
* **Crisis Language Detection**: Identifies urgent medical language without symptom-focused prompting
* **Pain Scale Assessment**: Recognizes severe pain indicators (8-10/10 scale references)
* **Emergency Keywords**: Detects direct emergency statements ("call ambulance", "emergency room")
* **Self-Harm Detection**: Identifies suicide/self-harm language patterns
* **Medication Crisis**: Detects potential overdose or dangerous medication interactions

**Emergency Classification Logic:**
```python
def detect_medical_emergency(self, text, user_profile):
    """
    Emergency indicators include:
    - Direct emergency statements: "call ambulance", "emergency room"
    - Severe pain indicators: "worst pain ever", "10/10 pain"
    - Crisis language: "can't breathe", "chest crushing"
    - Suicide/self-harm language
    - Medication overdose indicators
    """
    emergency_score = self.calculate_emergency_score(text)
    
    if emergency_score > EMERGENCY_THRESHOLD:
        return {
            'is_emergency': True,
            'emergency_type': self.classify_emergency_type(text),
            'confidence': emergency_score,
            'recommended_action': 'IMMEDIATE_INTERVENTION'
        }
```

**Emergency Notification Protocol:**
```python
async def handle_emergency(self, user_id, emergency_data):
    # 1. Immediate staff/admin notification
    await self.notify_staff_emergency(user_id, emergency_data)
    
    # 2. Emergency contact notification via WhatsApp
    emergency_contacts = get_user_emergency_contacts(user_id)
    patient_link = generate_secure_patient_link(user_id)
    
    message = f"""
    🚨 MEDICAL ALERT: {user_name} may need immediate medical attention.
    
    Emergency detected: {emergency_data.type}
    Time: {datetime.now()}
    
    View patient details: {patient_link}
    
    If this is a false alarm, please contact us immediately.
    """
    
    for contact in emergency_contacts:
        await send_whatsapp_emergency_alert(contact.phone, message)
    
    # 3. Log emergency event with full audit trail
    await log_emergency_event(user_id, emergency_data)
```

## 2.6.3. User Data Control & Opt-Out Management

**Opt-Out Capability:**
* **Full Opt-Out**: Users can completely disable agent processing
* **Selective Opt-Out**: Disable specific agent functions while keeping others
* **Emergency Override**: Emergency detection can remain active even with opt-out
* **Data Preservation**: Raw data stored separately for manual review when opted out

**Data Handling Strategy:**
```python
def process_user_input(self, user_id, input_text, channel):
    user_prefs = get_user_preferences(user_id)
    
    if not user_prefs.agent_processing_enabled:
        # Store in raw data folder for manual review
        return store_raw_data(user_id, input_text, channel, 'opted_out')
    
    # Emergency detection ALWAYS runs (unless explicitly disabled)
    if user_prefs.emergency_detection_enabled:
        emergency_check = self.detect_medical_emergency(input_text, user_id)
        if emergency_check.is_emergency:
            await self.handle_emergency(user_id, emergency_check)
    
    # Regular processing for consenting users
    return self.process_medical_data(input_text, user_id, channel)
```

## 2.6.4. Smart Escalation & Staff Notification

**Escalation Framework:**
```python
class EscalationManager:
    def __init__(self):
        self.escalation_rules = {
            'EMERGENCY': {
                'notify': ['on_call_doctor', 'admin'], 
                'sla': '0_minutes',
                'priority': 'CRITICAL'
            },
            'TIME_SENSITIVE_DATA': {
                'notify': ['assigned_nurse', 'primary_physician'], 
                'sla': '15_minutes',
                'priority': 'URGENT'
            },
            'DATA_CONFLICT': {
                'notify': ['assigned_staff'], 
                'sla': '2_hours',
                'priority': 'HIGH'
            },
            'LOW_CONFIDENCE': {
                'notify': ['data_review_team'], 
                'sla': '4_hours',
                'priority': 'MEDIUM'
            },
            'PROCESSING_FAILURE': {
                'notify': ['technical_team'], 
                'sla': '1_hour',
                'priority': 'HIGH'
            }
        }
```

**Staff Notification System:**
```python
async def escalate(self, escalation_type, user_id, context):
    rule = self.escalation_rules[escalation_type]
    
    for role in rule['notify']:
        available_staff = get_available_staff(role)
        
        notification = {
            'type': escalation_type,
            'user_id': user_id,
            'patient_link': generate_secure_patient_link(user_id),
            'context': context,
            'sla': rule['sla'],
            'priority': rule['priority'],
            'requires_immediate_action': escalation_type == 'EMERGENCY'
        }
        
        await send_staff_notification(available_staff, notification)
```

## 2.6.5. Database Schema for Data Management System

**Core Data Management Tables:**
```sql
-- Main medical records (for users who consent to agent processing)
CREATE TABLE medical_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    data_type VARCHAR(100), -- 'blood_pressure', 'medication', 'symptom', 'vital_sign'
    value JSONB,
    source_channel VARCHAR(20), -- 'web', 'whatsapp'
    source_conversation_id UUID,
    agent_processed BOOLEAN DEFAULT TRUE,
    confidence_score DECIMAL(3,2),
    processing_timestamp TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Raw data storage (for opt-out users or unprocessed data)
CREATE TABLE raw_medical_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    raw_input TEXT,
    source_channel VARCHAR(20),
    source_conversation_id UUID,
    processing_status VARCHAR(50), -- 'opted_out', 'pending_review', 'failed_processing', 'emergency_detected'
    emergency_flags JSONB, -- Emergency detection results even for opt-out users
    created_at TIMESTAMP DEFAULT NOW()
);

-- User data preferences
CREATE TABLE user_data_preferences (
    user_id UUID PRIMARY KEY REFERENCES users(id),
    agent_processing_enabled BOOLEAN DEFAULT TRUE,
    emergency_detection_enabled BOOLEAN DEFAULT TRUE,
    emergency_contacts JSONB, -- Array of emergency contact objects
    data_sharing_consent JSONB, -- Granular consent settings
    opt_out_date TIMESTAMP,
    opt_out_reason TEXT,
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Agent processing logs
CREATE TABLE agent_processing_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    input_text TEXT,
    analysis_result JSONB,
    confidence_score DECIMAL(3,2),
    actions_taken JSONB,
    conflicts_detected JSONB,
    escalations_triggered JSONB,
    processing_time_ms INT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Emergency events
CREATE TABLE emergency_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    emergency_type VARCHAR(100), -- 'medical_crisis', 'self_harm', 'medication_overdose'
    detected_content TEXT,
    confidence_score DECIMAL(3,2),
    source_channel VARCHAR(20),
    notifications_sent JSONB, -- Record of all notifications sent
    staff_response JSONB, -- Staff acknowledgment and actions
    resolution_status VARCHAR(50), -- 'open', 'acknowledged', 'resolved', 'false_alarm'
    created_at TIMESTAMP DEFAULT NOW(),
    resolved_at TIMESTAMP
);

-- Staff notifications
CREATE TABLE staff_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    staff_id UUID REFERENCES users(id),
    notification_type VARCHAR(100),
    patient_id UUID REFERENCES users(id),
    priority VARCHAR(20), -- 'CRITICAL', 'URGENT', 'HIGH', 'MEDIUM', 'LOW'
    content JSONB,
    patient_link TEXT, -- Secure link to patient details
    sla_deadline TIMESTAMP,
    status VARCHAR(50), -- 'sent', 'read', 'acknowledged', 'resolved'
    acknowledged_at TIMESTAMP,
    resolved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Data conflicts and resolutions
CREATE TABLE data_conflicts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    data_type VARCHAR(100),
    existing_value JSONB,
    new_value JSONB,
    conflict_reason TEXT,
    resolution_strategy VARCHAR(100), -- 'auto_resolved', 'staff_review', 'user_confirmation'
    resolved_value JSONB,
    confidence_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT NOW(),
    resolved_at TIMESTAMP
);
```

## 2.6.6. API Endpoints for Data Management

**Agent Management Endpoints:**
* `/api/v1/data-agent/process` (POST - Internal: Process user input through agent)
* `/api/v1/data-agent/status` (GET - Agent system health and status)
* `/api/v1/data-agent/preferences/{userId}` (GET, PUT - User data processing preferences)
* `/api/v1/data-agent/emergency-contacts/{userId}` (GET, PUT - Emergency contact management)
* `/api/v1/data-agent/opt-out/{userId}` (POST - Process user opt-out request)
* `/api/v1/data-agent/conflicts/{userId}` (GET - View data conflicts for user)
* `/api/v1/data-agent/logs/{userId}` (GET - Processing logs for compliance)

**Staff/Admin Endpoints:**
* `/api/v1/staff/notifications/` (GET - Staff notification queue)
* `/api/v1/staff/notifications/{notificationId}/acknowledge` (POST - Acknowledge notification)
* `/api/v1/staff/emergency-events/` (GET - Emergency events requiring attention)
* `/api/v1/staff/data-conflicts/` (GET - Data conflicts requiring review)
* `/api/v1/admin/agent-analytics/` (GET - Agent performance and statistics)
* `/api/v1/admin/emergency-events/` (GET - All emergency events for oversight)

## 2.6.7. Integration Points

**Service Integration:**
* **Chat Service**: Forwards all user input to Data Management Agent for processing
* **WhatsApp Service**: Sends WhatsApp messages through agent analysis pipeline
* **User Service**: Provides user preferences and emergency contact information
* **Staff Service**: Receives escalation notifications and emergency alerts
* **Notification Service**: Handles emergency contact notifications and staff alerts
* **Medical Records Service**: Stores processed medical data with agent metadata
* **Analytics Service**: Tracks agent performance and emergency response metrics

**Cross-Channel Data Flow:**
```
User Input (Web/WhatsApp) → Data Management Agent → Analysis & Processing
                                      ↓
                            Medical Records Storage ← Conflict Resolution
                                      ↓
                        Emergency Detection → Staff/Contact Notifications
                                      ↓
                              Audit Logging & Analytics
```

### 2.7. Medical Record Management

* **Record Creation/Update:** Implement logic (likely within the Chat Service or a dedicated processing task) to parse user conversations and automatically create/update structured entries in the user's `medical_records` table in Supabase (associating entries with conversation IDs/timestamps).
* **Secure Storage (Supabase):**
    * Utilize Supabase (PostgreSQL) for storage.
    * Ensure encryption at rest is enabled for the database/sensitive columns.
    * Ensure encryption in transit (TLS/SSL) via Supabase configuration and HTTPS enforcement at the gateway/API level.
    * Apply strict RLS policies (See Section 2.4) to control access.
    * Configure regular, secure backups using Supabase features (e.g., PITR).
* **Record Retrieval:** The `/api/records/me` endpoint must retrieve the authenticated user's records from Supabase, respecting RLS policies.
* **Record Export/Sharing:**
    * Implement backend functionality (`/api/records/me/export`) to generate a formatted document (e.g., PDF) of the user's record from Supabase data. Include source attribution and associated doctor names (if stored).
    * Implement a secure mechanism for sharing (`/api/records/me/share`), requiring user consent and potentially using time-limited secure links or other agreed-upon methods.

### 2.8. Audit Logging

* **Comprehensive Logging:** Implement detailed audit logging for *all* security-relevant actions, especially administrative ones. Use the `pgAudit` extension on Supabase.
* **Logged Events (Minimum):** Successful/failed logins (user & admin), session creation/termination/revocation, password changes/resets, MFA changes, CRUD operations on users (admin & end-user), role/permission assignments/changes, access/modification/export of medical records, significant configuration changes, API calls to sensitive endpoints, concurrent session violations, suspicious session activity, WhatsApp phone verification attempts, WhatsApp account linking/unlinking, cross-channel conversation switches, WhatsApp message delivery status.
* **Log Entry Data:** Timestamp (UTC), Acting User ID (user/admin), User Role(s) at time of action, Source IP Address, Action Performed (standardized taxonomy), Target Resource ID (if applicable), Success/Failure Status.
* **Retention & Security:** Define the log retention period (min. 6 years for HIPAA). Ensure logs are stored securely and are tamper-evident. Provide secure access for authorized administrators (e.g., Compliance Auditor role via `/api/admin/audit-logs/`).
* **Monitoring & Alerting:** Implement monitoring of audit logs for suspicious patterns (e.g., multiple failed admin logins, privilege escalations) and configure real-time alerts for critical events.

### 2.9. Analytics Service (FastAPI)

**Overview:**  
The Analytics Service provides comprehensive data collection, processing, and reporting capabilities for system usage, AI performance, medical compliance, and business intelligence while ensuring GDPR/NDPA compliance through robust anonymization techniques.

**Core Functionality:**
* **Event Tracking System:**
    * Implement event taxonomy for user actions (login, logout, conversation start/end, appointment booking, record access)
    * Track AI interactions (query types, response times, user satisfaction ratings, conversation completion rates)
    * Monitor medical record access patterns (creation, viewing, sharing, export events)
    * Log system performance metrics (API response times, error rates, concurrent users)
    * Track multi-channel usage (web vs WhatsApp interactions, cross-channel switches)

* **Real-Time Analytics Pipeline:**
    * Implement high-throughput event ingestion via `/api/v1/analytics/event` (POST)
    * Use Redis for real-time metric aggregation and caching
    * Batch processing for complex analytics computations
    * Support for streaming analytics using tools like Apache Kafka (optional for high-volume scenarios)

* **Data Anonymization & Compliance:**
    * Implement k-anonymity (minimum group size of 5) for user behavior analysis
    * Apply differential privacy techniques for sensitive health-related metrics
    * Automatic PII removal and hash-based user identification for analytics
    * Configurable data retention policies (GDPR: 13 months default, HIPAA: 6 years for audit trails)
    * Data export capabilities for compliance audits and user data requests

* **Dashboard & Reporting:**
    * Real-time system health dashboard (active users, response times, error rates)
    * AI performance analytics (average response quality scores, conversation completion rates, most common query types)
    * User engagement metrics (daily/monthly active users, feature adoption rates, session duration)
    * Medical compliance reporting (consent tracking, data access patterns, audit trail summaries)
    * Staff productivity analytics (appointment completion rates, patient interaction metrics)

* **AI Performance Monitoring:**
    * Response quality scoring using feedback and conversation completion rates
    * Track agent performance within the AI swarm (response times, success rates, error patterns)
    * Monitor LLM usage and costs via Requestly AI router
    * A/B testing framework for AI model improvements
    * Conversation flow analysis to identify common user journey patterns

* **Alerting & Anomaly Detection:**
    * Real-time alerts for system anomalies (sudden traffic spikes, error rate increases, security incidents)
    * AI performance degradation detection (response quality drops, increased error rates)
    * Compliance violation alerts (unusual data access patterns, failed consent checks)
    * Usage pattern anomalies (potential security threats, system abuse)

**API Endpoints:**
* `/api/v1/analytics/event` (POST - Track individual events)
* `/api/v1/analytics/events/batch` (POST - Bulk event submission)
* `/api/v1/analytics/dashboard/realtime` (GET - Live system metrics)
* `/api/v1/analytics/reports/usage` (GET - User engagement reports)
* `/api/v1/analytics/reports/ai-performance` (GET - AI system performance)
* `/api/v1/analytics/reports/compliance` (GET - Compliance and audit reports)
* `/api/v1/analytics/alerts/` (GET, POST - Alert configuration and history)
* `/api/v1/analytics/export` (POST - Data export for compliance)
* `/api/v1/analytics/retention/policy` (GET, PUT - Data retention settings)

**Database Schema:**
```sql
CREATE TABLE analytics_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL, -- 'user_action', 'ai_interaction', 'system_metric'
    category VARCHAR(100), -- 'login', 'conversation', 'appointment', 'record_access'
    user_hash VARCHAR(64), -- Anonymized user identifier
    session_hash VARCHAR(64), -- Anonymized session identifier
    metadata JSONB, -- Event-specific data
    timestamp TIMESTAMP DEFAULT NOW(),
    ip_hash VARCHAR(64), -- Anonymized IP for security tracking
    user_agent_hash VARCHAR(64), -- Anonymized user agent
    channel VARCHAR(20) DEFAULT 'web' -- 'web', 'whatsapp', 'api'
);

CREATE TABLE analytics_aggregates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_name VARCHAR(100),
    metric_value DECIMAL,
    dimensions JSONB, -- Grouping dimensions
    period_start TIMESTAMP,
    period_end TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE analytics_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    alert_type VARCHAR(100),
    condition_config JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    last_triggered TIMESTAMP,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);
```

**Integration & Services:**
* Analytics Service integrates with all other services for event collection
* Uses Redis for real-time metrics caching and aggregation
* Connects to external monitoring tools (optional: Prometheus, Grafana)
* Supports webhook notifications for critical alerts

### 2.10. Notification Service

**Overview:**  
The Notification Service provides multi-channel notification delivery with intelligent scheduling, personalization, and compliance-aware messaging for appointment reminders, medical updates, security alerts, and system notifications.

**Core Functionality:**
* **Multi-Channel Delivery System:**
    * **Email:** SMTP integration with SendGrid/AWS SES for transactional and marketing emails
    * **SMS:** Twilio integration for appointment reminders and urgent notifications
    * **Push Notifications:** Integration with Firebase Cloud Messaging (FCM) for mobile/web push
    * **In-App Notifications:** Real-time notifications via WebSocket connections
    * **WhatsApp:** Integration with WhatsApp Business API for template messages

* **Template Management System:**
    * Dynamic template engine with personalization variables (user name, appointment details, medical info)
    * Multi-language template support with localization framework
    * Template versioning and A/B testing capabilities
    * HIPAA-compliant medical notification templates
    * WhatsApp Business approved templates for medical communications

* **Intelligent Scheduling & Delivery:**
    * Scheduled delivery for appointment reminders (24h, 1h, 15min before)
    * Time zone-aware scheduling for global users and staff
    * Delivery optimization based on user preferences and engagement history
    * Retry logic with exponential backoff for failed deliveries
    * Rate limiting to prevent spam and comply with provider limits

* **User Preference Management:**
    * Granular notification preferences (type, channel, frequency, time windows)
    * Opt-in/opt-out management with audit trails for compliance
    * Emergency notification overrides for critical medical alerts
    * Channel fallback (SMS if email fails for urgent notifications)
    * Do-not-disturb scheduling (quiet hours, weekends)

* **Priority-Based Delivery:**
    * **Emergency:** Immediate delivery across all enabled channels (security breaches, critical medical alerts)
    * **Urgent:** Priority delivery within 5 minutes (appointment changes, staff assignments)
    * **Normal:** Standard delivery within 1 hour (general reminders, updates)
    * **Low Priority:** Batched delivery (daily/weekly summaries, marketing)

* **Delivery Tracking & Analytics:**
    * Comprehensive delivery status tracking (sent, delivered, opened, clicked, failed)
    * Read receipts and engagement analytics
    * Bounce and unsubscribe handling
    * Delivery performance metrics and optimization insights
    * Integration with Analytics Service for notification effectiveness tracking

* **Compliance & Security:**
    * HIPAA-compliant medical information handling in notifications
    * Encrypted storage of notification content and user preferences
    * Audit logging for all notification activities
    * Consent management integration for marketing communications
    * Secure unsubscribe mechanisms with immediate effect

**API Endpoints:**
* `/api/v1/notifications/send` (POST - Send immediate notification)
* `/api/v1/notifications/schedule` (POST - Schedule future notification)
* `/api/v1/notifications/templates/` (GET, POST, PUT, DELETE - Template management)
* `/api/v1/notifications/preferences/` (GET, PUT - User notification preferences)
* `/api/v1/notifications/history/` (GET - Notification history for user)
* `/api/v1/notifications/status/{notificationId}` (GET - Delivery status tracking)
* `/api/v1/notifications/unsubscribe/{token}` (POST - Secure unsubscribe)
* `/api/v1/notifications/analytics` (GET - Delivery and engagement metrics)
* `/api/v1/notifications/bulk` (POST - Bulk notification sending)

**Database Schema:**
```sql
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) UNIQUE,
    type VARCHAR(100), -- 'email', 'sms', 'push', 'whatsapp', 'in_app'
    category VARCHAR(100), -- 'appointment', 'medical', 'security', 'marketing'
    subject_template TEXT,
    body_template TEXT,
    variables JSONB, -- Template variable definitions
    language VARCHAR(10) DEFAULT 'en',
    is_active BOOLEAN DEFAULT TRUE,
    compliance_approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    type VARCHAR(50), -- 'email', 'sms', 'push', 'whatsapp', 'in_app'
    priority VARCHAR(20), -- 'emergency', 'urgent', 'normal', 'low'
    subject TEXT,
    content TEXT,
    scheduled_for TIMESTAMP,
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    opened_at TIMESTAMP,
    status VARCHAR(50), -- 'pending', 'sent', 'delivered', 'failed', 'opened'
    failure_reason TEXT,
    retry_count INT DEFAULT 0,
    metadata JSONB, -- Delivery tracking data
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE user_notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    category VARCHAR(100), -- 'appointment', 'medical', 'security', 'marketing'
    email_enabled BOOLEAN DEFAULT TRUE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    push_enabled BOOLEAN DEFAULT TRUE,
    whatsapp_enabled BOOLEAN DEFAULT FALSE,
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    timezone VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE notification_delivery_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID REFERENCES notifications(id),
    provider VARCHAR(100), -- 'sendgrid', 'twilio', 'fcm', 'whatsapp'
    provider_message_id VARCHAR(255),
    delivery_status VARCHAR(50),
    response_data JSONB,
    delivered_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

**Integration Points:**
* **Appointment Service:** Automated appointment reminder scheduling
* **Medical Records Service:** Medical update notifications with compliance checks
* **Authentication Service:** Security alert notifications (login anomalies, password changes)
* **Chat Service:** New message notifications and conversation updates
* **Staff Service:** Staff assignment notifications and patient updates
* **Analytics Service:** Notification performance tracking and optimization

**Third-Party Integrations:**
* **SendGrid/AWS SES:** Email delivery with advanced analytics
* **Twilio:** SMS delivery with international support
* **Firebase Cloud Messaging:** Push notifications for web and mobile
* **WhatsApp Business API:** Template message delivery
* **Timezone APIs:** Global timezone handling for accurate scheduling

### 2.11. Appointment Service (Comprehensive)

**Overview:**  
The Appointment Service provides a complete scheduling solution with intelligent availability management, multi-timezone support, automated reminders, conflict resolution, and seamless integration with staff calendars and notification systems.

**Core Functionality:**
* **Advanced Scheduling Logic:**
    * Dynamic time slot generation based on staff availability and appointment types
    * Configurable appointment durations (15min, 30min, 45min, 60min, custom)
    * Buffer time management between appointments (5-15min configurable)
    * Recurring appointment support (weekly, monthly check-ups)
    * Emergency slot allocation with priority override capabilities
    * Group appointment scheduling for family consultations

* **Availability Management:**
    * Staff working hours configuration with per-day granularity
    * Holiday and leave management with automatic slot blocking
    * Real-time availability checking to prevent double-booking
    * Capacity management for group sessions and walk-ins
    * Location-based availability (clinic rooms, virtual consultation setup)
    * Break time scheduling and automatic slot protection

* **Multi-Timezone Support:**
    * Global timezone handling for international patients
    * Automatic timezone conversion for staff and patient displays
    * Daylight saving time awareness and adjustment
    * Timezone conflict detection and resolution
    * Multi-region staff scheduling coordination

* **Intelligent Booking Rules:**
    * Advance booking limits (minimum 2h, maximum 90 days configurable)
    * Cancellation policies with configurable deadlines (24h, 48h, 72h)
    * Rescheduling limitations and approval workflows
    * Patient history-based booking preferences
    * Staff specialization matching for appointment types
    * Insurance and payment verification integration points

* **Conflict Resolution & Validation:**
    * Real-time double-booking prevention with distributed locks
    * Staff availability validation across multiple services
    * Patient scheduling conflict detection (overlapping appointments)
    * Automatic waitlist management for fully booked slots
    * Priority-based scheduling for urgent medical needs
    * Resource availability checking (medical equipment, rooms)

* **Waitlist & Queue Management:**
    * Automated waitlist enrollment for unavailable time slots
    * Intelligent waitlist notification when slots become available
    * Priority-based waitlist ordering (medical urgency, booking time)
    * Automatic appointment offering with time-limited acceptance
    * Queue position tracking and estimated wait time calculation
    * Batch waitlist processing for efficiency

* **Appointment Lifecycle Management:**
    * Status tracking (scheduled, confirmed, in-progress, completed, cancelled, no-show)
    * Automated status transitions based on time and staff actions
    * Pre-appointment preparation workflows (document collection, questionnaires)
    * Post-appointment follow-up scheduling and note-taking
    * Appointment rating and feedback collection
    * Billing integration for completed appointments

**API Endpoints:**
* `/api/v1/appointments/` (GET, POST - List and create appointments)
* `/api/v1/appointments/{appointmentId}` (GET, PUT, DELETE - Manage specific appointment)
* `/api/v1/appointments/availability/` (GET - Check staff availability)
* `/api/v1/appointments/availability/slots` (GET - Get available time slots)
* `/api/v1/appointments/reschedule/{appointmentId}` (PUT - Reschedule appointment)
* `/api/v1/appointments/cancel/{appointmentId}` (PUT - Cancel appointment)
* `/api/v1/appointments/confirm/{appointmentId}` (PUT - Confirm appointment)
* `/api/v1/appointments/waitlist/` (GET, POST - Waitlist management)
* `/api/v1/appointments/waitlist/{appointmentId}/notify` (POST - Notify waitlist)
* `/api/v1/appointments/history/` (GET - User appointment history)
* `/api/v1/appointments/upcoming/` (GET - Upcoming appointments)
* `/api/v1/appointments/search` (GET - Search appointments with filters)
* `/api/v1/appointments/bulk-update` (PUT - Bulk status updates)
* `/api/v1/appointments/analytics/` (GET - Appointment analytics)

**Staff-Specific Endpoints:**
* `/api/v1/staff/appointments/` (GET - Staff appointment list)
* `/api/v1/staff/appointments/availability` (PUT - Update availability)
* `/api/v1/staff/appointments/schedule` (GET - Staff schedule view)
* `/api/v1/staff/appointments/{appointmentId}/notes` (POST, PUT - Appointment notes)
* `/api/v1/staff/appointments/working-hours` (GET, PUT - Working hours configuration)

**Database Schema:**
```sql
CREATE TABLE appointment_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    duration_minutes INT NOT NULL DEFAULT 30,
    buffer_minutes INT DEFAULT 10,
    description TEXT,
    requires_preparation BOOLEAN DEFAULT FALSE,
    allows_virtual BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES users(id),
    staff_id UUID REFERENCES users(id),
    appointment_type_id UUID REFERENCES appointment_types(id),
    scheduled_start TIMESTAMP NOT NULL,
    scheduled_end TIMESTAMP NOT NULL,
    actual_start TIMESTAMP,
    actual_end TIMESTAMP,
    status VARCHAR(50) DEFAULT 'scheduled', -- 'scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'
    location VARCHAR(255), -- 'virtual', 'clinic_room_1', etc.
    notes TEXT,
    preparation_completed BOOLEAN DEFAULT FALSE,
    reminder_sent BOOLEAN DEFAULT FALSE,
    cancellation_reason TEXT,
    rescheduled_from UUID REFERENCES appointments(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE staff_availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    staff_id UUID REFERENCES users(id),
    day_of_week INT, -- 0=Sunday, 1=Monday, etc.
    start_time TIME,
    end_time TIME,
    timezone VARCHAR(50),
    is_available BOOLEAN DEFAULT TRUE,
    effective_from DATE,
    effective_until DATE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE appointment_waitlist (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id UUID REFERENCES users(id),
    staff_id UUID REFERENCES users(id),
    appointment_type_id UUID REFERENCES appointment_types(id),
    preferred_start_date DATE,
    preferred_end_date DATE,
    preferred_time_start TIME,
    preferred_time_end TIME,
    priority INT DEFAULT 1, -- 1=normal, 2=urgent, 3=emergency
    status VARCHAR(50) DEFAULT 'active', -- 'active', 'offered', 'converted', 'expired'
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP
);

CREATE TABLE staff_time_off (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    staff_id UUID REFERENCES users(id),
    start_date DATE,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    reason VARCHAR(255),
    type VARCHAR(50), -- 'vacation', 'sick', 'conference', 'break'
    is_approved BOOLEAN DEFAULT FALSE,
    approved_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE appointment_reminders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    appointment_id UUID REFERENCES appointments(id),
    reminder_type VARCHAR(50), -- '24h', '1h', '15min'
    scheduled_for TIMESTAMP,
    sent_at TIMESTAMP,
    delivery_method VARCHAR(50), -- 'email', 'sms', 'whatsapp', 'push'
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'sent', 'failed'
    created_at TIMESTAMP DEFAULT NOW()
);
```

**Integration Points:**
* **Staff Service:** Staff availability, specialization, and assignment management
* **Notification Service:** Automated reminder scheduling and delivery
* **User Service:** Patient information and preferences
* **Medical Records Service:** Pre/post appointment record updates
* **Analytics Service:** Booking patterns, no-show rates, staff utilization
* **WhatsApp Service:** WhatsApp-based appointment booking and confirmations

**Advanced Features:**
* **Smart Scheduling:** AI-powered optimal slot suggestions based on historical patterns
* **Resource Management:** Integration with clinic resource booking (rooms, equipment)
* **Insurance Verification:** Real-time insurance coverage checking
* **Telemedicine Integration:** Virtual appointment room creation and management
* **Mobile Calendar Sync:** Two-way sync with staff mobile calendars
* **Patient Portal Integration:** Self-service rescheduling and cancellation

### 2.12. WhatsApp Service (Multi-Channel Integration)

**Overview:**  
The WhatsApp Service enables users to interact with the PETALSHEALTHAI system via WhatsApp Business API, providing seamless multi-channel access to AI health agents while maintaining unified user identity and data synchronization across web and WhatsApp platforms.

**Core Functionality:**
* **WhatsApp Business API Integration:** Implement WhatsApp Business API client for sending/receiving messages, handling webhooks, and managing conversation flows.
* **Phone Number Authentication:** Handle phone number verification via OTP for new WhatsApp users and account linking for existing web users.
* **Multi-Channel User Identity:** Maintain unified user profiles across web and WhatsApp channels using phone number as secondary identifier.
* **Message Processing:** Parse WhatsApp messages, handle media (images, documents, voice notes), and convert to standardized format for AI processing.
* **Cross-Channel Data Sync:** Ensure medical records, conversations, and user data are synchronized between web and WhatsApp interactions.

**Authentication & User Management:**
* **Phone Number Verification:** Implement OTP-based verification for new WhatsApp users via `/api/v1/whatsapp/auth/verify-phone`.
* **Account Linking:** Enable existing web users to link WhatsApp numbers via `/api/v1/whatsapp/link-account`.
* **Unified Sessions:** Extend Authentication Service to handle WhatsApp sessions with phone-based identifiers.
* **Cross-Platform Identity:** Support users switching between web and WhatsApp mid-conversation while maintaining context.

**Message Flow & AI Integration:**
* **Webhook Management:** Handle incoming WhatsApp messages via webhook endpoints (`/api/v1/whatsapp/webhook`).
* **Message Queue:** Implement reliable message processing using queues for webhook reliability and rate limiting compliance.
* **AI Orchestrator Integration:** Forward processed messages to AI Orchestrator Service, maintaining conversation context across channels.
* **Response Formatting:** Convert AI responses to WhatsApp-compatible formats (text, buttons, lists, media).
* **Template Messages:** Support WhatsApp Business approved templates for appointment reminders and notifications.

**Data Synchronization Architecture:**
* **Unified Conversations:** Store all conversations (web + WhatsApp) in shared `conversations` table with channel identifier.
* **Cross-Channel Medical Records:** Parse WhatsApp conversations and update unified medical records accessible from both channels.
* **Real-Time Sync:** Ensure changes made via web interface are reflected in WhatsApp conversations and vice versa.

**WhatsApp-Specific Features:**
* **Media Handling:** Support image analysis for medical photos, document processing for lab results/prescriptions.
* **Voice Message Processing:** Implement speech-to-text for voice messages with medical context understanding.
* **Interactive Elements:** Utilize WhatsApp buttons, quick replies, and lists for appointment scheduling and medical questionnaires.
* **Compliance Templates:** Use pre-approved WhatsApp templates for HIPAA-compliant communication.

**Rate Limiting & Compliance:**
* **WhatsApp Quotas:** Implement rate limiting to comply with WhatsApp Business messaging quotas and avoid account suspension.
* **Medical Use Compliance:** Ensure WhatsApp Business Terms compliance for medical conversations and health information sharing.
* **Data Residency:** Handle WhatsApp data routing considerations for HIPAA and international data transfer requirements.
* **Consent Management:** Implement WhatsApp-specific consent mechanisms for medical data processing and sharing.

**Integration Patterns:**
```
WhatsApp Business API
        ↓
WhatsApp Service ←→ Authentication Service (Phone verification)
        ↓
Message Queue ←→ Chat Service (Shared logic)
        ↓
AI Orchestrator ←→ Medical Records Service
        ↓
User Service ←→ Analytics Service
```

**API Endpoints:**
* `/api/v1/whatsapp/webhook` (POST - Receive WhatsApp messages)
* `/api/v1/whatsapp/auth/verify-phone` (POST - OTP verification)
* `/api/v1/whatsapp/auth/resend-otp` (POST - Resend verification code)
* `/api/v1/whatsapp/link-account` (POST - Link existing web account)
* `/api/v1/whatsapp/conversations/` (GET, POST - WhatsApp conversation management)
* `/api/v1/whatsapp/conversations/{conversationId}` (GET - WhatsApp conversation history)
* `/api/v1/whatsapp/send-message` (POST - Send outbound messages)
* `/api/v1/whatsapp/templates/` (GET - Available message templates)
* `/api/v1/whatsapp/media/upload` (POST - Upload media for sending)
* `/api/v1/whatsapp/status` (GET - WhatsApp Business API status)
* `/api/v1/whatsapp/analytics` (GET - WhatsApp-specific metrics)

**Database Schema Extensions:**
```sql
-- Extend users table
ALTER TABLE users ADD COLUMN phone_number VARCHAR(20) UNIQUE;
ALTER TABLE users ADD COLUMN phone_verified_at TIMESTAMP;
ALTER TABLE users ADD COLUMN whatsapp_enabled BOOLEAN DEFAULT FALSE;

-- Conversations table update
ALTER TABLE conversations ADD COLUMN channel VARCHAR(20) DEFAULT 'web'; -- 'web' | 'whatsapp'
ALTER TABLE conversations ADD COLUMN whatsapp_conversation_id VARCHAR(255);
ALTER TABLE conversations ADD COLUMN phone_number VARCHAR(20);

-- WhatsApp-specific tables
CREATE TABLE whatsapp_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES conversations(id),
    whatsapp_message_id VARCHAR(255) UNIQUE,
    message_type VARCHAR(50), -- 'text', 'image', 'document', 'voice', 'button_reply'
    content TEXT,
    media_url TEXT,
    direction VARCHAR(20), -- 'inbound', 'outbound'
    status VARCHAR(50), -- 'sent', 'delivered', 'read', 'failed'
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE whatsapp_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) UNIQUE,
    category VARCHAR(100), -- 'appointment', 'notification', 'alert'
    content TEXT,
    status VARCHAR(50), -- 'approved', 'pending', 'rejected'
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE phone_verifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone_number VARCHAR(20),
    otp_code VARCHAR(10),
    attempts INT DEFAULT 0,
    verified BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 2.13. Staff Management (Doctors, Nurses, and Other Medical Roles)

**Overview:**  
The backend will support a dedicated "Staff" domain for healthcare professionals (doctors, nurses, specialists, lab technicians, pharmacists, etc.) who interact with users (patients) and manage their care. Staff will have a separate frontend and a dedicated backend API namespace.

**Staff Roles & Permissions:**
- **Roles:** Doctor, Nurse, Specialist, Lab Technician, Pharmacist, Therapist, and other medical staff as needed.
- **Granular Permissions:**  
  - Each role has default permissions (see Table 2 below).
  - Staff can grant/revoke other staff access to their assigned patients' data (e.g., a doctor can allow a specialist to view a patient's records for a consult, and revoke later).
  - All permission changes are logged and auditable.
- **Table 2: Example Staff Roles and Permissions**

| Role             | Default Permissions                                                                                                   | Can Grant/Revoke Access To...         |
|------------------|----------------------------------------------------------------------------------------------------------------------|---------------------------------------|
| Doctor           | View/update assigned patients' records, manage appointments, chat with patients/admin, add observations              | Other staff for their patients        |
| Nurse            | View/update assigned patients' records, manage appointments, add observations, chat with patients/admin              | Other staff for their patients        |
| Specialist       | View/update assigned/shared patients' records, add observations, chat with patients/admin                            | Other staff for their patients        |
| Lab Technician   | View assigned patients' records, upload lab results, add observations                                                | Other staff for their patients        |
| Pharmacist       | View assigned patients' records, add medication notes, chat with patients/admin                                      | Other staff for their patients        |
| Therapist        | View/update assigned patients' records, add therapy notes, manage appointments, chat with patients/admin             | Other staff for their patients        |

**Account Lifecycle:**
- **Self-Registration:** Staff can register via `/api/v1/staff/register`.
- **Verification & Approval:**  
  - Registration triggers a verification process (e.g., license upload, credential check).
  - Admin reviews and approves/rejects staff accounts via `/api/v1/admin/staff/verify`.
  - Only approved staff can access the system.
- **Deactivation/Deletion:** Admins can deactivate or delete staff accounts as needed.

**Authentication:**
- **Supabase Auth** is used for staff authentication.
- **MFA is mandatory** for all staff logins (enforced via Supabase settings or custom logic).
- **Password Policies:** Same as admin (strong password, breach checks, lockout policies).
- **Secure Recovery:** Secure process for staff account recovery, with admin oversight.

**Authorization & Data Access:**
- **Assigned Patients:**  
  - Staff can only access data for patients assigned to them or shared by other staff.
  - Assignment is managed by admin or by staff with appropriate permissions.
- **Shared Access:**  
  - Staff can grant/revoke other staff access to their patients' data (with audit logging).
- **Medical Records:**  
  - Staff cannot edit existing records; they can only add updates/observations.
  - All updates are versioned; previous records are retained.
  - If multiple staff are assigned to a patient, no single staff can overwrite another's updates; all observations are tracked with timestamps and author.
- **Appointments:**  
  - Staff can manage (create/update/cancel) appointments for their assigned patients.
- **Conversations:**  
  - Staff can chat with their assigned patients, with other staff (for consults), and with admins.
  - All chat history is stored and auditable.
- **AI Search:**  
  - Staff can use AI-powered search endpoints to query patient records and data.

**API Namespace & Endpoints:**
- All staff-related endpoints are under `/api/v1/staff/`.
- Example endpoints:
  - `/api/v1/staff/register` (POST)
  - `/api/v1/staff/login` (POST)
  - `/api/v1/staff/me` (GET, PUT, DELETE)
  - `/api/v1/staff/patients/` (GET - list assigned/shared patients)
  - `/api/v1/staff/patients/{patientId}` (GET - view patient, POST - add observation)
  - `/api/v1/staff/appointments/` (GET, POST, PUT, DELETE)
  - `/api/v1/staff/conversations/` (GET, POST)
  - `/api/v1/staff/ai-search/` (POST - AI-powered record/data search)
  - `/api/v1/staff/notifications/` (GET)
  - `/api/v1/staff/access/` (POST - grant/revoke access to other staff)
  - `/api/v1/staff/analytics/` (GET - staff dashboard, usage metrics)

**Audit Logging & Compliance:**
- All staff actions (logins, data access, updates, permission changes, chats, etc.) are logged using `pgAudit`.
- Logs include: timestamp, staff ID, role, action, patient ID (if applicable), success/failure, and source IP.
- **Compliance:**  
  - Enforce HIPAA, GDPR, NDPA, MDR, and ePrivacy requirements for staff.
  - Manage informed patient consent for data access and sharing.
  - Regular audits and ongoing training for staff on compliance and security.

**Notifications:**
- Staff receive notifications for:
  - New patient assignments
  - Appointment reminders
  - Messages from patients
  - Messages from admin
  - Patient follow-up reminders

**Integration & Services:**
- Staff backend service is implemented in Node.js + Express.js (in `/services/staff-service`).
- Staff service integrates with:
  - User Service (for patient data)
  - Medical Record Management Service (for record updates)
  - Appointment Service (for scheduling)
  - Chat Service (for conversations)
  - Notification Service (for alerts)
  - AI Orchestrator Service (for AI-powered search)
- Staff service enforces all RBAC/RLS policies for data access.

### 2.14. Payment Service (Future Implementation)

**Overview:**  
The Payment Service provides secure, compliant, and flexible payment processing for appointment fees, subscription services, premium features, and medical consultations while ensuring PCI DSS compliance and supporting multiple payment methods globally.

**Core Functionality:**
* **Multi-Payment Gateway Support:**
    * Primary integration with Stripe for global card processing
    * PayPal integration for alternative payment methods
    * Apple Pay and Google Pay for mobile payments
    * Bank transfer support for enterprise clients
    * Cryptocurrency payment options (future consideration)
    * Regional payment methods (M-Pesa, UPI, Alipay, etc.)

* **Payment Processing & Management:**
    * One-time payments for consultations and appointments
    * Subscription billing for premium AI features and unlimited access
    * Invoice generation and management for institutional clients
    * Payment plan support for expensive treatments or procedures
    * Refund processing with configurable policies
    * Partial payments and installment plans

* **Pricing & Billing Models:**
    * Tiered subscription plans (Basic, Premium, Professional)
    * Pay-per-consultation pricing with dynamic rates based on staff specialization
    * Freemium model with limited AI interactions for free users
    * Enterprise pricing for healthcare institutions
    * Geographic pricing adjustments for global accessibility
    * Insurance integration for covered consultations

* **Financial Compliance & Security:**
    * PCI DSS Level 1 compliance for card data handling
    * SOX compliance for financial reporting
    * Anti-money laundering (AML) checks for high-value transactions
    * Know Your Customer (KYC) verification for enterprise accounts
    * Tax calculation and reporting for multiple jurisdictions
    * Audit trails for all financial transactions

* **Revenue & Analytics:**
    * Revenue analytics and reporting dashboards
    * Payment success/failure rate monitoring
    * Chargeback and dispute management
    * Financial forecasting and trend analysis
    * Staff commission tracking and payouts
    * Tax reporting and compliance documentation

**API Endpoints:**
* `/api/v1/payments/methods/` (GET, POST, DELETE - Payment method management)
* `/api/v1/payments/process` (POST - Process one-time payment)
* `/api/v1/payments/subscriptions/` (GET, POST, PUT, DELETE - Subscription management)
* `/api/v1/payments/invoices/` (GET, POST - Invoice generation and retrieval)
* `/api/v1/payments/refunds/` (POST, GET - Refund processing and status)
* `/api/v1/payments/history/` (GET - Payment history for user)
* `/api/v1/payments/billing-info/` (GET, PUT - Billing information management)
* `/api/v1/payments/plans/` (GET - Available subscription plans)
* `/api/v1/payments/webhooks/stripe` (POST - Stripe webhook handler)
* `/api/v1/payments/analytics/` (GET - Payment analytics for admin)

**Database Schema:**
```sql
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price_monthly DECIMAL(10,2),
    price_yearly DECIMAL(10,2),
    features JSONB, -- List of included features
    ai_message_limit INT, -- NULL for unlimited
    appointment_limit INT, -- NULL for unlimited
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    plan_id UUID REFERENCES subscription_plans(id),
    status VARCHAR(50), -- 'active', 'cancelled', 'past_due', 'expired'
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    stripe_subscription_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    cancelled_at TIMESTAMP
);

CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    appointment_id UUID REFERENCES appointments(id), -- NULL for subscriptions
    subscription_id UUID REFERENCES user_subscriptions(id), -- NULL for one-time
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method VARCHAR(50), -- 'card', 'paypal', 'bank_transfer'
    status VARCHAR(50), -- 'pending', 'completed', 'failed', 'refunded'
    gateway VARCHAR(50), -- 'stripe', 'paypal'
    gateway_transaction_id VARCHAR(255),
    failure_reason TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

CREATE TABLE payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    type VARCHAR(50), -- 'card', 'bank_account', 'paypal'
    last_four VARCHAR(4), -- Last 4 digits for cards
    brand VARCHAR(50), -- 'visa', 'mastercard', etc.
    stripe_payment_method_id VARCHAR(255),
    is_default BOOLEAN DEFAULT FALSE,
    expires_at DATE,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    invoice_number VARCHAR(100) UNIQUE,
    amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(50), -- 'draft', 'sent', 'paid', 'overdue', 'cancelled'
    due_date DATE,
    paid_at TIMESTAMP,
    line_items JSONB, -- Invoice line items
    billing_address JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE refunds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    payment_id UUID REFERENCES payments(id),
    amount DECIMAL(10,2) NOT NULL,
    reason VARCHAR(255),
    status VARCHAR(50), -- 'pending', 'completed', 'failed'
    gateway_refund_id VARCHAR(255),
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

**Integration Points:**
* **Appointment Service:** Payment verification before appointment confirmation
* **User Service:** Subscription status checking and feature access control
* **Notification Service:** Payment reminders, receipts, and billing notifications
* **Analytics Service:** Revenue tracking and payment performance metrics
* **Staff Service:** Commission calculation and payout management

**Compliance Requirements:**
* **PCI DSS:** Secure card data handling through tokenization
* **GDPR/NDPA:** Secure handling of financial data with right to erasure
* **Regional Tax Laws:** Automatic tax calculation based on user location
* **Healthcare Billing:** Integration with insurance systems for covered services
* **Anti-Fraud:** Machine learning-based fraud detection and prevention

**Future Enhancements:**
* **Insurance Integration:** Direct billing to insurance providers
* **HSA/FSA Support:** Health Savings Account and Flexible Spending Account payments
* **Cryptocurrency Payments:** Bitcoin, Ethereum support for privacy-conscious users
* **Buy Now Pay Later:** Integration with Klarna, Afterpay for appointment fees
* **Corporate Billing:** Bulk billing for enterprise healthcare plans

---

## Environment Variables Configuration

**Based on the services and integrations outlined in this PRD, the following environment variables need to be configured:**

### Core Application Settings
```bash
# Application Environment
NODE_ENV=development|staging|production
PORT=3000
API_VERSION=v1
APP_NAME=PETALSHEALTHAI
APP_URL=https://your-domain.com
API_BASE_URL=https://api.your-domain.com

# Security
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_REFRESH_SECRET=your-refresh-token-secret
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
ENCRYPTION_KEY=your-32-character-encryption-key
BCRYPT_SALT_ROUNDS=12
```

### Database Configuration (Supabase)
```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
SUPABASE_JWT_SECRET=your-supabase-jwt-secret

# Database Connection (if direct PostgreSQL access needed)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
DB_HOST=db.your-project.supabase.co
DB_PORT=5432
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=your-database-password
DB_SSL=true
```

### Redis Configuration (Session Management & Caching)
```bash
# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
REDIS_SESSION_DB=1
REDIS_CACHE_DB=2
REDIS_TTL=3600
```

### AI & LLM Services
```bash
# Requestly AI API Router
REQUESTLY_AI_API_KEY=your-requestly-ai-api-key
REQUESTLY_AI_BASE_URL=https://api.requestly.ai

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORG_ID=your-openai-org-id
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000

# Anthropic Configuration
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-sonnet

# Google AI Configuration
GOOGLE_AI_API_KEY=your-google-ai-api-key
GOOGLE_AI_PROJECT_ID=your-google-project-id

# Local LLM Configuration (if using)
LOCAL_LLM_URL=http://localhost:8080
LOCAL_LLM_MODEL=llama2-7b
```

### External Health APIs
```bash
# MedlinePlus API
MEDLINEPLUS_API_KEY=your-medlineplus-api-key
MEDLINEPLUS_BASE_URL=https://wsearch.nlm.nih.gov/ws/query

# Exercise API
EXERCISE_API_KEY=your-exercise-api-key
EXERCISE_API_BASE_URL=https://api.api-ninjas.com/v1/exercises

# Air Quality API
AIR_QUALITY_API_KEY=your-air-quality-api-key
AIR_QUALITY_BASE_URL=https://api.airnow.gov

# FoodData Central API
FOODDATA_API_KEY=your-fooddata-api-key
FOODDATA_BASE_URL=https://api.nal.usda.gov/fdc/v1

# Recipe APIs
SPOONACULAR_API_KEY=your-spoonacular-api-key
EDAMAM_API_KEY=your-edamam-api-key
EDAMAM_APP_ID=your-edamam-app-id
```

### Communication Services
```bash
# WhatsApp Business API
WHATSAPP_BUSINESS_ACCOUNT_ID=your-whatsapp-business-account-id
WHATSAPP_ACCESS_TOKEN=your-whatsapp-access-token
WHATSAPP_PHONE_NUMBER_ID=your-whatsapp-phone-number-id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your-webhook-verify-token
WHATSAPP_API_VERSION=v18.0
WHATSAPP_BASE_URL=https://graph.facebook.com

# Twilio (SMS)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********
TWILIO_MESSAGING_SERVICE_SID=your-messaging-service-sid

# SendGrid (Email)
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=PETALSHEALTHAI

# Google Cloud Email (Gmail API / Cloud Email)
GCP_PROJECT_ID=your-gcp-project-id
GCP_SERVICE_ACCOUNT_KEY_PATH=./config/gcp-service-account.json
GMAIL_API_CLIENT_ID=your-gmail-api-client-id
GMAIL_API_CLIENT_SECRET=your-gmail-api-client-secret

# Firebase Cloud Messaging (Push Notifications)
FCM_SERVER_KEY=your-fcm-server-key
FCM_PROJECT_ID=your-firebase-project-id
FIREBASE_ADMIN_SDK_KEY_PATH=./config/firebase-admin-sdk.json
```

### Payment Services
```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret
STRIPE_API_VERSION=2023-10-16

# PayPal Configuration
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox|live
PAYPAL_WEBHOOK_ID=your-paypal-webhook-id
```

### File Storage & Media
```bash
# Supabase Storage
SUPABASE_STORAGE_BUCKET=medical-files
SUPABASE_STORAGE_URL=https://your-project.supabase.co/storage/v1

# Google Cloud Storage (Alternative Storage)
GCS_BUCKET_NAME=petalshealthai-files
GCS_PROJECT_ID=your-gcp-project-id
GCS_SERVICE_ACCOUNT_KEY_PATH=./config/gcp-storage-service-account.json
GCS_REGION=us-central1

# File Upload Configuration
MAX_FILE_SIZE=********  # 10MB in bytes
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx
UPLOAD_PATH=./uploads
```

### Security & Compliance
```bash
# Security Headers
CORS_ORIGIN=https://your-frontend-domain.com,https://admin.your-domain.com
CORS_CREDENTIALS=true
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# HIPAA Compliance
HIPAA_AUDIT_LOG_RETENTION_YEARS=6
ENCRYPTION_AT_REST_ENABLED=true
AUDIT_LOG_ENCRYPTION_KEY=your-audit-log-encryption-key

# Session Security
SESSION_SECRET=your-session-secret-key
SESSION_TIMEOUT_MINUTES=60
MAX_CONCURRENT_SESSIONS=3
SECURE_COOKIES=true
SAME_SITE_POLICY=strict
```

### Monitoring & Analytics
```bash
# Application Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
LOG_LEVEL=info|debug|warn|error
LOG_FORMAT=json|text

# Google Cloud Monitoring & Logging
GCP_MONITORING_PROJECT_ID=your-gcp-project-id
GCP_LOGGING_ENABLED=true
GOOGLE_CLOUD_LOGGING_SERVICE_ACCOUNT_PATH=./config/gcp-logging-service-account.json

# Analytics Configuration
ANALYTICS_RETENTION_DAYS=90
ANALYTICS_ANONYMIZATION_ENABLED=true
ANALYTICS_BATCH_SIZE=1000

# Health Check Configuration
HEALTH_CHECK_INTERVAL_SECONDS=30
HEALTH_CHECK_TIMEOUT_SECONDS=5
```

### Express Gateway Configuration
```bash
# API Gateway
GATEWAY_PORT=8080
GATEWAY_HOST=0.0.0.0
GATEWAY_RATE_LIMIT_MAX=1000
GATEWAY_RATE_LIMIT_WINDOW=3600000  # 1 hour

# Service URLs
USER_SERVICE_URL=http://localhost:3001
AUTH_SERVICE_URL=http://localhost:3002
MEDICAL_RECORDS_SERVICE_URL=http://localhost:3003
CHAT_SERVICE_URL=http://localhost:3004
WHATSAPP_SERVICE_URL=http://localhost:3005
APPOINTMENT_SERVICE_URL=http://localhost:3006
NOTIFICATION_SERVICE_URL=http://localhost:3007
STAFF_SERVICE_URL=http://localhost:3008
PAYMENT_SERVICE_URL=http://localhost:3009

# New Health-Specific Services
SUPPLEMENT_OPTIMIZATION_SERVICE_URL=http://localhost:8002
PREDICTIVE_HEALTH_SERVICE_URL=http://localhost:8003
MENTAL_HEALTH_SERVICE_URL=http://localhost:8004
TELEMEDICINE_SERVICE_URL=http://localhost:3010
LABORATORY_INTEGRATION_SERVICE_URL=http://localhost:3011
EMERGENCY_RESPONSE_SERVICE_URL=http://localhost:8005
CLINICAL_DECISION_SUPPORT_SERVICE_URL=http://localhost:8006
IOT_WEARABLES_SERVICE_URL=http://localhost:3012

# AI & Analytics Services
AI_ORCHESTRATOR_URL=http://localhost:8000
ANALYTICS_SERVICE_URL=http://localhost:8001
```

### Development & Testing
```bash
# Development Configuration
DEBUG=true
MOCK_EXTERNAL_APIS=false
SKIP_AUTH_IN_DEV=false

# Package Manager Configuration
PACKAGE_MANAGER_NODE=bun  # Use Bun for all Node.js services
PACKAGE_MANAGER_PYTHON=uv  # Use UV for all FastAPI services
BUN_INSTALL_CACHE_DIR=./.bun-cache
UV_CACHE_DIR=./.uv-cache

# Testing Configuration
TEST_DATABASE_URL=postgresql://postgres:password@localhost:5432/petalshealthai_test
TEST_REDIS_URL=redis://localhost:6379/15
RUN_INTEGRATION_TESTS=true

# Seed Data
SEED_ADMIN_EMAIL=<EMAIL>
SEED_ADMIN_PASSWORD=SecureAdminPassword123!
SEED_DEMO_DATA=false
```

### Compliance & Legal
```bash
# Data Protection
GDPR_ENABLED=true
NDPA_ENABLED=true
HIPAA_ENABLED=true
DATA_RETENTION_POLICY_DAYS=2555  # 7 years
RIGHT_TO_ERASURE_ENABLED=true

# Emergency Settings
EMERGENCY_CONTACT_NOTIFICATION=true
EMERGENCY_ESCALATION_TIMEOUT_MINUTES=5
CRISIS_INTERVENTION_ENABLED=true
```

### Multi-Tenancy & Internationalization
```bash
# Internationalization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de,pt
TIMEZONE_DEFAULT=UTC

# Regional Settings
DEFAULT_CURRENCY=USD
SUPPORTED_CURRENCIES=USD,EUR,GBP,CAD,AUD
TAX_CALCULATION_ENABLED=true
```

### Backup & Disaster Recovery
```bash
# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION_ENABLED=true

# Disaster Recovery
DR_ENABLED=true
DR_REPLICATION_INTERVAL_HOURS=6
DR_FAILOVER_TIMEOUT_SECONDS=300
```

---

### Google Cloud Platform Specific
```bash
# Google Cloud Platform Core
GCP_PROJECT_ID=your-gcp-project-id
GCP_REGION=us-central1
GCP_ZONE=us-central1-a
GOOGLE_APPLICATION_CREDENTIALS=./config/gcp-service-account.json

# Google Cloud Secret Manager
GCP_SECRET_MANAGER_ENABLED=true
GCP_SECRET_PROJECT_ID=your-gcp-project-id

# Google Cloud Functions (if using serverless)
GCF_RUNTIME=nodejs18
GCF_MEMORY=512MB
GCF_TIMEOUT=60s

# Google Cloud SQL (if using managed PostgreSQL instead of Supabase)
CLOUD_SQL_CONNECTION_NAME=your-project:us-central1:your-instance
CLOUD_SQL_DATABASE_NAME=petalshealthai
CLOUD_SQL_USERNAME=postgres
CLOUD_SQL_PASSWORD=your-cloud-sql-password

# Google Cloud Pub/Sub (for async messaging)
PUBSUB_PROJECT_ID=your-gcp-project-id
PUBSUB_TOPIC_NOTIFICATIONS=health-notifications
PUBSUB_TOPIC_ANALYTICS=health-analytics
PUBSUB_SUBSCRIPTION_MEDICAL_PROCESSING=medical-data-processing

# Google Cloud AI Platform (if using Google AI services)
GOOGLE_AI_PLATFORM_PROJECT=your-gcp-project-id
GOOGLE_VERTEX_AI_REGION=us-central1
GOOGLE_VERTEX_AI_ENDPOINT=your-vertex-ai-endpoint
```

**Note:** 
1. Replace all placeholder values with your actual credentials and configurations.
2. Store sensitive environment variables securely using **Google Cloud Secret Manager** in production.
3. Use different values for development, staging, and production environments.
4. Ensure all API keys have appropriate scopes and permissions for security.
5. Regularly rotate secrets and API keys as per security best practices.
6. Consider using `.env.example` files in your repository with placeholder values for team onboarding.
7. **Google Cloud Service Accounts**: Create separate service accounts for different services with minimal required permissions.
8. **IAM Roles**: Follow principle of least privilege when assigning IAM roles to service accounts.
9. **Package Managers**: Ensure **Bun** is installed for Node.js services and **UV** is installed for Python/FastAPI services.
10. **Development Setup**: Use `bun install` for Node.js dependencies and `uv pip install` for Python dependencies.

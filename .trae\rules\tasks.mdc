---
description: Reference for Task Master task data structure and management
globs: **/*
alwaysApply: true
---

# Task Data Structure Reference

This document provides a comprehensive reference for the Task Master task data structure and management.

## Task Object Structure

Each task in Task Master is represented as a JSON object with the following fields:

```json
{
  "id": "1",
  "title": "Initialize Repository",
  "description": "Set up the initial repository structure and configuration.",
  "status": "pending",
  "dependencies": [],
  "priority": "high",
  "details": "Create a new repository with the following structure...",
  "testStrategy": "Verify that all files are created with the correct content...",
  "subtasks": [
    {
      "id": "1.1",
      "title": "Create package.json",
      "description": "Initialize npm package with required dependencies.",
      "status": "pending",
      "details": "Run npm init and add the following dependencies..."
    }
  ]
}
```

### Field Descriptions

#### Required Fields

- **id**: Unique identifier for the task.
  - Format: String, typically numeric or dot-separated for subtasks (e.g., "1", "1.1").
  - Example: `"id": "1"`

- **title**: Brief, descriptive title for the task.
  - Format: String, concise but informative.
  - Example: `"title": "Initialize Repository"`

- **status**: Current state of the task.
  - Format: String, one of the standard statuses or custom value.
  - Standard values: "pending", "done", "deferred".
  - Example: `"status": "pending"`

#### Optional Fields

- **description**: Concise summary of what the task involves.
  - Format: String, 1-2 sentences.
  - Example: `"description": "Set up the initial repository structure and configuration."`

- **dependencies**: IDs of prerequisite tasks that must be completed before this task.
  - Format: Array of strings (task IDs).
  - Example: `"dependencies": ["1", "2.3"]`

- **priority**: Importance level of the task.
  - Format: String, typically "high", "medium", or "low".
  - Example: `"priority": "high"`

- **details**: In-depth implementation instructions.
  - Format: String, can include markdown formatting.
  - Example: `"details": "Create a new repository with the following structure...\n- src/\n- tests/\n- docs/"`

- **testStrategy**: Approach for verifying task completion.
  - Format: String, can include markdown formatting.
  - Example: `"testStrategy": "Verify that all files are created with the correct content..."`

- **subtasks**: List of smaller, more specific tasks that make up this task.
  - Format: Array of task objects (without their own subtasks).
  - Example: See the example above.

## Task Status Values

Task Master uses the following standard status values:

- **pending**: The task is ready to be worked on but has not been started or completed.
- **done**: The task has been completed and verified.
- **deferred**: The task has been postponed and is not currently active.

Custom status values can be used as needed for project-specific workflows.

## Task Priority Values

Task Master uses the following standard priority values:

- **high**: Critical tasks that should be completed as soon as possible.
- **medium**: Important tasks that should be completed after high-priority tasks.
- **low**: Tasks that should be completed when time permits.

Custom priority values can be used as needed for project-specific workflows.

## Task ID Format

Task IDs follow a hierarchical format:

- Top-level tasks have simple numeric IDs: "1", "2", "3", etc.
- Subtasks append a dot and a number to their parent's ID: "1.1", "1.2", "2.1", etc.
- Sub-subtasks continue the pattern: "1.1.1", "1.1.2", etc.

This format makes it easy to identify the hierarchy of tasks and their relationships.

## Task File Format

When tasks are generated as files, they follow this standard format:

```markdown
# Task 1: Initialize Repository

**Status:** pending  
**Priority:** high  
**Dependencies:** None

## Description

Set up the initial repository structure and configuration.

## Implementation Details

Create a new repository with the following structure...
- src/
- tests/
- docs/

## Test Strategy

Verify that all files are created with the correct content...

## Subtasks

### Subtask 1.1: Create package.json

**Status:** pending

#### Description

Initialize npm package with required dependencies.

#### Implementation Details

Run npm init and add the following dependencies...
```

## Task Management Best Practices

1. **Clear Titles and Descriptions**: Write clear, concise titles and descriptions that communicate the purpose and scope of each task.

2. **Appropriate Granularity**: Break down large tasks into smaller, manageable subtasks, but avoid creating too many tiny tasks.

3. **Accurate Dependencies**: Ensure that task dependencies accurately reflect the required order of implementation.

4. **Detailed Implementation Instructions**: Provide sufficient details in the "details" field to guide implementation.

5. **Verification Criteria**: Include clear test strategies that define when a task can be considered complete.

6. **Regular Status Updates**: Keep task statuses up to date to reflect the current state of the project.

7. **Consistent Formatting**: Maintain consistent formatting and style across all tasks.

8. **Documentation**: Document any changes to task structure or status for future reference.

## Related Rules

- [`dev_workflow.mdc`](mdc:.trae/rules/dev_workflow.mdc): Guidelines for using Task Master in the development workflow.
- [`taskmaster.mdc`](mdc:.trae/rules/taskmaster.mdc): Reference for Task Master MCP tools and CLI commands.
- [`tests.mdc`](mdc:.trae/rules/tests.mdc): Guidelines for testing strategies and practices.
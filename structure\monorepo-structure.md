# PETALSHEALTHAI Monorepo Structure (Detailed)

```
/ (repo root)
│
├── user/                        # User-facing frontend (Next.js, TypeScript, shadcn)
│   ├── app/                     # Next.js app directory
│   ├── components/              # Reusable React components
│   ├── lib/                     # Client-side utilities/helpers
│   ├── public/                  # Static assets
│   ├── styles/                  # Global and component styles
│   ├── tests/                   # Unit/integration tests
│   └── ...                      # Config, env, etc.
│
├── admin/                       # Admin portal frontend (Next.js, TypeScript, shadcn)
│   ├── app/
│   ├── components/
│   ├── lib/
│   ├── public/
│   ├── styles/
│   ├── tests/
│   └── ...
│
├── staff/                       # Staff portal frontend (doctors, nurses, etc.)
│   ├── app/
│   ├── components/
│   ├── lib/
│   ├── public/
│   ├── styles/
│   ├── tests/
│   └── ...
│
├── services/                    # All backend microservices and API gateway
│   ├── gateway/                 # API Gateway (Express Gateway, Node.js, Bun)
│   │   ├── config/
│   │   │   └── gateway.config.yml   # Express Gateway main config
│   │   ├── policies/                # Custom gateway policies (rate limiting, auth, etc.)
│   │   ├── plugins/                 # Custom plugins/extensions
│   │   ├── logs/                    # Gateway logs
│   │   ├── scripts/                 # Gateway utility scripts
│   │   └── ...
│   ├── user-service/                # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── auth-service/                # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── medical-record-service/      # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── chat-service/                # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── whatsapp-service/            # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── appointment-service/         # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── staff-service/               # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── notification-service/        # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── payment-service/             # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── telemedicine-service/        # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── laboratory-integration-service/ # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── iot-wearables-service/       # Node.js/Express.js (Bun)
│   │   ├── src/
│   │   ├── routes/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── middlewares/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── ai-orchestrator-service/     # FastAPI (Python, UV)
│   │   ├── app/                 # FastAPI app
│   │   ├── agents/              # Specialized AI agents
│   │   ├── models/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── analytics-service/           # FastAPI (Python, UV)
│   │   ├── app/                 # FastAPI app
│   │   ├── models/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── supplement-optimization-service/ # FastAPI (Python, UV)
│   │   ├── app/
│   │   ├── models/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── predictive-health-service/       # FastAPI (Python, UV)
│   │   ├── app/
│   │   ├── models/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── mental-health-service/           # FastAPI (Python, UV)
│   │   ├── app/
│   │   ├── models/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── emergency-response-service/      # FastAPI (Python, UV)
│   │   ├── app/
│   │   ├── models/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   ├── clinical-decision-support-service/ # FastAPI (Python, UV)
│   │   ├── app/
│   │   ├── models/
│   │   ├── utils/
│   │   ├── tests/
│   │   └── ...
│   └── ...                      # (Future: add more as needed)
│
├── tasks/                       # Task Master tasks and planning files
│
├── scripts/                     # PRD, setup, and utility scripts
│
└── structure/                   # Project structure documentation (this file)
```

**Notes:**
- Each frontend app (user, admin, staff) follows Next.js best practices and is isolated for role-based UX and security.
- The API Gateway (Express Gateway) is now inside /services as /services/gateway, serving as the single entry point for all frontend requests (routing, auth, rate limiting, etc.).
- Each backend service is modular, with clear separation of concerns (routes, controllers, models, etc.).
- Add new services as the project evolves (e.g., EHR integration, wearable data, etc.).
- This structure supports scalable, secure, and maintainable development. 
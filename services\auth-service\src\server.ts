/**
 * PETALSHEALTHAI Authentication Service
 * 
 * This service handles user and admin authentication, session management,
 * JWT token validation, and multi-factor authentication.
 * 
 * Features:
 * - User registration and login
 * - Admin authentication with MFA
 * - Session management with Redis
 * - JWT token generation and validation
 * - Password reset functionality
 * - Phone number verification for WhatsApp integration
 * 
 * Tech Stack: Node.js, Express.js, Bun (package manager)
 * Port: 3001
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { config } from 'dotenv';
import winston from 'winston';
import { createClient } from 'redis';
import { createClient as createSupabaseClient } from '@supabase/supabase-js';

// Load environment variables
config();

const app = express();
const PORT = process.env.AUTH_SERVICE_PORT || 3001;

// Initialize logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new winston.transports.File({ 
      filename: 'logs/auth-service.log',
      level: 'info'
    }),
    new winston.transports.File({ 
      filename: 'logs/auth-service-error.log',
      level: 'error'
    })
  ]
});

// Initialize Redis client
const redisClient = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379'
});

redisClient.on('error', (err) => {
  logger.error('Redis Client Error:', err);
});

redisClient.on('connect', () => {
  logger.info('Connected to Redis successfully');
});

// Initialize Supabase client
const supabase = createSupabaseClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'production' ? 100 : 1000, // Limit each IP
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use((req, res, next) => {
  logger.info('Incoming request', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'auth-service',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

// API routes (to be implemented)
app.use('/api/v1/auth', (req, res, next) => {
  // TODO: Implement auth routes
  res.status(501).json({
    error: 'Auth routes not yet implemented',
    availableEndpoints: [
      'POST /api/v1/auth/register',
      'POST /api/v1/auth/login',
      'POST /api/v1/auth/logout',
      'POST /api/v1/auth/refresh-token',
      'POST /api/v1/auth/forgot-password',
      'POST /api/v1/auth/reset-password',
      'POST /api/v1/auth/verify-phone',
      'POST /api/v1/auth/validate-session',
      'GET /api/v1/auth/me'
    ]
  });
});

// Admin auth routes
app.use('/api/v1/admin/auth', (req, res, next) => {
  // TODO: Implement admin auth routes
  res.status(501).json({
    error: 'Admin auth routes not yet implemented',
    availableEndpoints: [
      'POST /api/v1/admin/auth/login',
      'POST /api/v1/admin/auth/mfa/setup',
      'POST /api/v1/admin/auth/mfa/verify',
      'POST /api/v1/admin/auth/logout',
      'GET /api/v1/admin/auth/sessions'
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip
  });

  res.status(err.status || 500).json({
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message,
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Graceful shutdown handler
const gracefulShutdown = async () => {
  logger.info('Received shutdown signal, closing server gracefully...');
  
  try {
    await redisClient.quit();
    logger.info('Redis connection closed');
    
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start server
async function startServer() {
  try {
    // Connect to Redis
    await redisClient.connect();
    
    // Start the server
    app.listen(PORT, () => {
      logger.info(`🚀 Auth Service is running on port ${PORT}`, {
        port: PORT,
        environment: process.env.NODE_ENV || 'development',
        nodeVersion: process.version,
        timestamp: new Date().toISOString()
      });
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Start the server
startServer();

export default app;

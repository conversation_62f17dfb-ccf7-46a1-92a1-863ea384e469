import express from 'express';
import swaggerUi from 'swagger-ui-express';
import swaggerJsdoc from 'swagger-jsdoc';
import path from 'path';
import fs from 'fs';

const app = express();
const port = 8081; // A different port than the main gateway

// Read package.json for version info
const packageJson = JSON.parse(
  fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8')
);

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Petals Health AI API Gateway',
      version: packageJson.version,
      description:
        'This is the API Gateway for Petals Health AI, providing a unified entry point to various microservices.',
      license: {
        name: 'PROPRIETARY',
        url: 'https://petalshealth.com/terms',
      },
      contact: {
        name: 'Petals Health AI Support',
        url: 'https://petalshealth.com/support',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: 'http://localhost:8080/api/v1',
        description: 'Development server',
      },
      {
        url: 'https://api.petalshealth.com/api/v1',
        description: 'Production server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
        supabaseAuth: {
          type: 'oauth2',
          flows: {
            implicit: {
              authorizationUrl: '/api/v1/auth/authorize',
              scopes: {
                read: 'Read access',
                write: 'Write access',
              },
            },
          },
        },
      },
      responses: {
        UnauthorizedError: {
          description: 'Access token is missing or invalid',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  error: {
                    type: 'string',
                    example: 'Unauthorized access',
                  },
                },
              },
            },
          },
        },
        NotFoundError: {
          description: 'The specified resource was not found',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  error: {
                    type: 'string',
                    example: 'Resource not found',
                  },
                },
              },
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
    tags: [
      {
        name: 'Auth',
        description: 'Authentication and authorization endpoints',
      },
      {
        name: 'Users',
        description: 'User management endpoints',
      },
      {
        name: 'Medical Records',
        description: 'Patient medical records endpoints',
      },
      {
        name: 'Chat',
        description: 'Chat and messaging endpoints',
      },
      {
        name: 'Appointments',
        description: 'Appointment scheduling and management',
      },
      {
        name: 'AI',
        description: 'AI orchestration and analysis endpoints',
      },
    ],
  },
  apis: [
    './src/server.ts', 
    '../**/*/src/routes/*.ts',
    './config/swagger-definitions/**/*.yaml'
  ], // Path to the API docs
};

const specs = swaggerJsdoc(options);

// Add custom middleware to set CORS headers
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  next();
});

// Serve Swagger UI
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Petals Health AI API Documentation',
}));

// Serve raw OpenAPI spec
app.get('/api-docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(specs);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

app.listen(port, () => {
  console.log(`Swagger UI server listening on port ${port}`);
  console.log(`API documentation available at http://localhost:${port}/api-docs`);
});

{"name": "@petalshealthai/chat-service", "version": "1.0.0", "description": "Real-time Chat Service for PETALSHEALTHAI with WebSocket support", "main": "dist/server.js", "scripts": {"dev": "bun --watch src/server.ts", "build": "bun build src/server.ts --outdir ./dist", "start": "bun run dist/server.js", "test": "bun test", "test:watch": "bun test --watch", "test:coverage": "bun test --coverage", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"**/*.{ts,js,json}\"", "migrate": "bun run src/scripts/migrate.ts", "seed": "bun run src/scripts/seed.ts", "health": "curl -f http://localhost:3004/health || exit 1"}, "keywords": ["chat", "websocket", "real-time", "messaging", "healthcare", "ai-integration", "microservice"], "author": "PETALSHEALTHAI Team", "license": "PROPRIETARY", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "socket.io-redis": "^6.1.1", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "redis": "^4.6.13", "@supabase/supabase-js": "^2.38.5", "dotenv": "^16.3.1", "winston": "^3.11.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "sharp": "^0.33.1", "file-type": "^18.7.0", "mime-types": "^2.1.35", "zod": "^3.22.4", "axios": "^1.6.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.11", "@types/node": "^24.0.10", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "jest": "^29.7.0", "prettier": "^3.6.2", "socket.io-client": "^4.7.4", "supertest": "^6.3.3", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "packageManager": "bun@1.0.0"}
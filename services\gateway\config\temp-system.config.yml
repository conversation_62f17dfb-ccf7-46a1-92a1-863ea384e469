db:
  type: "Memory"
  redis:
    host: "localhost"
    port: 6379
plugins:
  jwt:
    package: express-gateway-plugin-jwt
    version: 1.0.0
    enabled: true
    config:
      secretOrPublicKey: your-super-secure-jwt-secret-key
  express-gateway-plugin-oauth2:
    package: express-gateway-plugin-oauth2
    version: 1.0.0
    enabled: true
    config:
      supabase:
        url: "https://ctmgkqgrozmefuvcvfwa.supabase.co"
        anonKey: "your-supabase-anon-key"
        serviceKey: "${SUPABASE_SERVICE_KEY}"
      tokenEndpoint: "/api/v1/auth/token"
      authorizationEndpoint: "/api/v1/auth/authorize"
      clientIdField: "client_id"
      clientSecretField: "client_secret"
      accessTokenLifetime: 3600
      refreshTokenLifetime: 2592000 # 30 days
      allowEmptyState: false
      tokenTypes:
        - "jwt"
      jwtSignOptions:
        algorithm: "HS256"
        expiresIn: "1h"



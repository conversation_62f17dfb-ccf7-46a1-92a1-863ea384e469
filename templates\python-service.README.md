# Python FastAPI Microservice Template

## Description
A modern Python microservice using FastAPI. Replace this text with your service's purpose.

## Getting Started

### Prerequisites
- Python 3.11+
- [Poetry](https://python-poetry.org/) (recommended)

### Installation
```sh
poetry install
```

### Development
```sh
poetry run uvicorn app.main:app --reload
```

### Production
```sh
poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## Environment Variables
- Copy `.env.example` to `.env` and fill in required values.

## Scripts
- `poetry run uvicorn app.main:app --reload` — Start in development mode
- `poetry run pytest` — Run tests
- `poetry run black .` — Format code
- `poetry run mypy .` — Type check

## Best Practices
- Keep all source code in `app/`
- Use environment variables for secrets/config
- Write modular, testable code
- Add tests in a `tests/` directory

---

_Replace this template with service-specific details as needed._ 
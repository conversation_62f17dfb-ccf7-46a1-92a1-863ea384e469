# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Bun
.bun-cache/
bun.lockb

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
/dist

# logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
*.env.docker

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Editor directories and files
.idea/
.vscode/

# OS specific
.DS_Store

# Misc
*.bak
*.tmp

# 🏗️ Petals Health AI - Project Structure

**Last Updated:** June 2025  
**Current Phase:** Task 1 - Supabase Database Setup  
**Architecture:** Microservices with API Gateway

---

## 📁 **Current Project Structure**

```
petals_health_ai/
├── 🏠 ROOT LEVEL
│   ├── .env                           # Environment variables (NEVER commit)
│   ├── .gitignore                     # Git ignore rules
│   ├── README-task-master.md          # Task management documentation
│   ├── project_structure.md           # 📍 THIS FILE - Living structure doc
│   └── .windsurfrules                 # Development rules
│
├── 🔧 SERVICES (Backend Microservices)
│   ├── .gitkeep                       # Placeholder (services to be created)
│   ├── [PLANNED] gateway/             # Express Gateway (Task 13)
│   │   └── project_structure.md       # 📄 Gateway service structure (planned)
│   ├── [PLANNED] auth-service/        # Authentication Service (Task 14)
│   │   └── project_structure.md       # 📄 Auth service structure (planned)
│   ├── [PLANNED] user-service/        # User Management (Task 15)
│   │   └── project_structure.md       # 📄 User service structure (planned)
│   ├── [PLANNED] admin-service/       # Admin Management (Task 16)
│   │   └── project_structure.md       # 📄 Admin service structure (planned)
│   ├── [PLANNED] medical-records/     # Medical Records (Task 18)
│   │   └── project_structure.md       # 📄 Medical records structure (planned)
│   ├── [PLANNED] chat-service/        # Chat & WebSocket (Task 19)
│   │   └── project_structure.md       # 📄 Chat service structure (planned)
│   ├── [PLANNED] ai-orchestrator/     # AI Agent Swarm (Task 20)
│   │   └── project_structure.md       # 📄 AI orchestrator structure (planned)
│   ├── [PLANNED] staff-service/       # Staff Management (Task 26)
│   │   └── project_structure.md       # 📄 Staff service structure (planned)
│   ├── [PLANNED] appointment-service/ # Appointments (Task 27)
│   │   └── project_structure.md       # 📄 Appointment service structure (planned)
│   ├── [PLANNED] notification-service/# Notifications (Task 28)
│   │   └── project_structure.md       # 📄 Notification service structure (planned)
│   ├── [PLANNED] analytics-service/   # Analytics (Task 30)
│   │   └── project_structure.md       # 📄 Analytics service structure (planned)
│   ├── [PLANNED] whatsapp-service/    # WhatsApp Integration (Task 24)
│   │   └── project_structure.md       # 📄 WhatsApp service structure (planned)
│   ├── [PLANNED] payment-service/     # Payments (Task 29)
│   │   └── project_structure.md       # 📄 Payment service structure (planned)
│   └── [PLANNED] shared/              # Shared utilities & libraries
│       └── project_structure.md       # 📄 Shared library structure (planned)
│
├── 🌐 FRONTEND APPLICATIONS
│   ├── landing/                       # ✅ Landing page (Next.js)
│   │   └── project_structure.md       # 📄 Landing app structure
│   ├── admin/                         # 📋 Admin dashboard (planned)
│   │   └── project_structure.md       # 📄 Admin app structure (planned)
│   ├── user/                          # 👤 User portal (planned)
│   │   └── project_structure.md       # 📄 User app structure (planned)
│   └── staff/                         # 👩‍⚕️ Staff portal (planned)
│       └── project_structure.md       # 📄 Staff app structure (planned)
│
├── 📜 SCRIPTS & DOCUMENTATION
│   ├── scripts/
│   │   ├── env-template.txt           # Environment template
│   │   ├── environment-setup-guide.md # Setup instructions
│   │   ├── prd.txt                    # Product Requirements
│   │   ├── prd_mod.txt                # Modified PRD
│   │   └── task-complexity-report.json # Task analysis
│   └── structure/
│       └── monorepo-structure.md      # Architecture decisions
│
└── 📋 TASK MANAGEMENT
    └── tasks/                         # Task Master files
```

---

## 📄 **Project Structure Documentation System**

Each service and application maintains its own `project_structure.md` file following these patterns:

### **Documentation Hierarchy**
- **Main Project**: `project_structure.md` (this file) - Overall architecture
- **Each Service**: `services/{service-name}/project_structure.md` - Service-specific structure
- **Each Frontend**: `{app-name}/project_structure.md` - App-specific structure

### **Auto-Update Rules**
- **File/folder creation** → Update relevant `project_structure.md`
- **Dependency changes** → Update corresponding structure file
- **Task completion** → Update structure documentation
- **Before commits** → Verify all structure files are current

### **Reference Links**
- **Landing App**: [landing/project_structure.md](landing/project_structure.md) (to be created)
- **Services**: Individual service structure files (to be created when services are built)

*See [.cursor/rules/project_structure_maintenance.mdc](.cursor/rules/project_structure_maintenance.mdc) for detailed maintenance rules.*

---

## 🔧 **Backend Services Architecture**

### **API Gateway Pattern**
- **Gateway Service** (`/services/gateway/`) - Express Gateway
  - Routes all API requests to appropriate microservices
  - Handles authentication, rate limiting, CORS
  - Service discovery and load balancing

### **Microservices Breakdown**

| Service | Technology | Port | Purpose | Status |
|---------|------------|------|---------|--------|
| **gateway** | Express Gateway | 8080 | API Gateway & Routing | 📋 Planned |
| **auth-service** | Node.js + Bun | 3001 | Authentication & JWT | 📋 Planned |
| **user-service** | Node.js + Bun | 3002 | User Management | 📋 Planned |
| **admin-service** | Node.js + Bun | 3003 | Admin Functions | 📋 Planned |
| **medical-records** | Node.js + Bun | 3004 | Medical Data | 📋 Planned |
| **chat-service** | Node.js + Socket.IO | 3005 | Real-time Chat | 📋 Planned |
| **ai-orchestrator** | FastAPI + Python | 3006 | AI Agent Swarm | 📋 Planned |
| **staff-service** | Node.js + Bun | 3007 | Staff Management | 📋 Planned |
| **appointment-service** | Node.js + Bun | 3008 | Scheduling | 📋 Planned |
| **notification-service** | Node.js + Bun | 3009 | Multi-channel Notifications | 📋 Planned |
| **analytics-service** | FastAPI + Python | 3010 | Data Analytics | 📋 Planned |
| **whatsapp-service** | Node.js + Bun | 3011 | WhatsApp Integration | 📋 Planned |
| **payment-service** | Node.js + Bun | 3012 | Stripe Integration | 📋 Planned |

---

## 🗄️ **Database Architecture**

### **Supabase PostgreSQL**
- **Primary Database:** Supabase (Task 1 - ⏳ In Progress)
- **Authentication:** Supabase Auth
- **Storage:** Supabase Storage (file uploads)
- **Real-time:** Supabase Realtime subscriptions

### **Database Tables** (planned)
```sql
-- User Management
users, roles, permissions, user_roles

-- Medical Data
medical_records, patients, appointments

-- Communication
conversations, messages, notifications

-- Staff Management
staff, patient_assignments

-- System
audit_logs, analytics_events
```

---

## 🛠️ **Technology Stack**

### **Backend**
- **Package Manager:** Bun (Node.js services)
- **Package Manager:** UV (Python services) 
- **Database:** Supabase (PostgreSQL)
- **Cache/Sessions:** Redis
- **API Gateway:** Express Gateway
- **WebSocket:** Socket.IO

### **Frontend**
- **Framework:** Next.js 14+ (App Router)
- **Styling:** Tailwind CSS
- **UI Components:** shadcn/ui
- **State Management:** React Context/Zustand

### **AI & External APIs**
- **LLM Router:** Requestly AI
- **NLP:** Hugging Face Transformers
- **Agent Framework:** LangChain
- **External APIs:** MedlinePlus, FoodData Central, etc.

---

## 🚀 **Development Phases**

### **Phase 1: Foundation** ⏳ Current
- [x] Project structure setup
- [ ] **Task 1:** Supabase database setup
- [ ] **Task 11:** Project configuration
- [ ] **Task 13:** Express Gateway

### **Phase 2: Core Services**
- [ ] **Task 14:** Authentication service
- [ ] **Task 15:** User service  
- [ ] **Task 16:** Admin service
- [ ] **Task 18:** Medical records service

### **Phase 3: AI & Communication**
- [ ] **Task 19:** Chat service
- [ ] **Task 20:** AI orchestrator
- [ ] **Task 24:** WhatsApp service

### **Phase 4: Advanced Features**
- [ ] **Task 26:** Staff service
- [ ] **Task 27:** Appointment service
- [ ] **Task 28:** Notification service
- [ ] **Task 30:** Analytics service

---

## 📝 **Service Templates**

### **Node.js Service Structure**
```
service-name/
├── src/
│   ├── controllers/
│   ├── models/
│   ├── routes/
│   ├── middleware/
│   ├── utils/
│   └── index.ts
├── tests/
├── package.json
├── tsconfig.json
├── Dockerfile
└── README.md
```

### **Python Service Structure**
```
service-name/
├── app/
│   ├── routers/
│   ├── models/
│   ├── services/
│   ├── utils/
│   └── main.py
├── tests/
├── requirements.txt
├── pyproject.toml
├── Dockerfile
└── README.md
```

---

## 🔄 **Update Log**

| Date | Phase | Changes | Updated By |
|------|-------|---------|------------|
| June 2025 | Foundation | Created main project structure document | Assistant |
| June 2025 | Foundation | Defined services architecture | Assistant |
| June 2025 | Foundation | Planned microservices breakdown | Assistant |
| June 2025 | Foundation | Added project structure documentation system | Assistant |
| June 2025 | Foundation | Created project_structure_maintenance.mdc rule | Assistant |

---

## 📋 **Next Steps**

1. **Complete Task 1:** Supabase project setup
2. **Update this file** when each service is created
3. **Add specific configurations** as services are implemented
4. **Document API contracts** between services

---

**📍 Remember:** Update this file whenever:
- New services are added
- Structure changes
- New phases begin
- Architecture decisions are made 
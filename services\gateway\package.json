{"name": "@petalshealthai/gateway", "version": "1.0.0", "description": "API Gateway for PETALSHEALTHAI using Express Gateway", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "bun run build && bun --watch src/server.ts", "build": "tsc", "test": "bun test", "test:watch": "bun test --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write \"**/*.{ts,js,json}\"", "health": "curl -f http://localhost:8080/health || exit 1", "swagger-dev": "bun --watch src/swagger-server.ts"}, "keywords": ["api-gateway", "express-gateway", "healthcare", "telemedicine", "microservices"], "author": "PETALSHEALTHAI Team", "license": "PROPRIETARY", "dependencies": {"@sentry/node": "^9.36.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-gateway": "^1.16.11", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "redis": "^4.6.13", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "winston": "^3.17.0"}, "devDependencies": {"@types/bun": "latest", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/node": "^20.10.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "eslint": "^8.56.0", "nodemon": "^3.0.2", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "packageManager": "bun@1.0.0", "private": true, "peerDependencies": {"typescript": "^5"}}
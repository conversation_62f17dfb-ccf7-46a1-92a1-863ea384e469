# Node.js/Bun Microservice Template

## Description
A modern Node.js/TypeScript microservice using Bun for fast installs and runtime. Replace this text with your service's purpose.

## Getting Started

### Prerequisites
- [Bun](https://bun.sh/) (recommended)
- Node.js (if not using Bun)

### Installation
```sh
bun install
```

### Development
```sh
bun run dev
```

### Production
```sh
bun run build
bun run start
```

## Environment Variables
- Copy `.env.example` to `.env` and fill in required values.

## Scripts
- `bun run dev` — Start in development mode
- `bun run build` — Build TypeScript
- `bun run start` — Start in production mode

## Best Practices
- Keep all source code in `src/`
- Use environment variables for secrets/config
- Write modular, testable code
- Add tests in a `tests/` directory

---

_Replace this template with service-specific details as needed._ 